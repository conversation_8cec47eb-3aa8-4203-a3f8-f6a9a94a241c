const { OrderHead, OrderProductItem, OrderServiceItem, OrderAttachment, User, Enterprise, Asset, Product, ProductFeature, ProductFeatureRelation, Employee, sequelize } = require('../models');
const { Op } = require('sequelize');
const { generateOrderId } = require('../utils/id_helper');

/**
 * 获取订单审核列表 - 显示所有待审核的订单
 */
const getOrdersForReview = async () => {
    return await OrderHead.findAll({
        where: {
            audit_status: '待审核'
        },
        include: [
            { model: User, as: 'user', attributes: ['name', 'mobile'] },
            { model: Enterprise, as: 'enterprise', attributes: ['id', 'name', 'enterprise_id'] },
            { model: Asset, as: 'asset' },
            { model: Employee, as: 'creator', attributes: ['name'] }
        ],
        order: [['created_at', 'DESC']]
    });
};

/**
 * 获取产品订单列表 - 显示已审核通过的产品订单
 */
const getProductOrders = async () => {
    return await OrderHead.findAll({
        where: {
            order_category: '产品订单',
            audit_status: '已审核'
        },
        include: [
            { model: User, as: 'user', attributes: ['name', 'mobile'] },
            { model: Enterprise, as: 'enterprise', attributes: ['id', 'name', 'enterprise_id'] },
            { model: Asset, as: 'asset' },
            { model: OrderProductItem, as: 'productItems', include: [{ model: Product, as: 'product' }] },
            { model: Employee, as: 'creator', attributes: ['name'] }
        ],
        order: [['created_at', 'DESC']]
    });
};

/**
 * 获取服务订单列表 - 显示已审核通过的服务订单
 */
const getServiceOrders = async () => {
    return await OrderHead.findAll({
        where: {
            order_category: '服务订单',
            audit_status: '已审核'
        },
        include: [
            { model: User, as: 'user', attributes: ['name', 'mobile'] },
            { model: Enterprise, as: 'enterprise', attributes: ['id', 'name', 'enterprise_id'] },
            { model: Asset, as: 'asset' },
            { model: OrderServiceItem, as: 'serviceItems' },
            { model: Employee, as: 'creator', attributes: ['name'] }
        ],
        order: [['created_at', 'DESC']]
    });
};

/**
 * 根据主键ID获取单个订单详情
 * @param {number} id - 订单主键ID
 * @param {object} transaction - 可选的事务对象
 */
const getOrderById = async (id, transaction = null) => {
    const options = {
        include: [
           { model: User, as: 'user' },
           { model: Enterprise, as: 'enterprise' },
           { model: Asset, as: 'asset', include: [{ model: Product, as: 'product' }] },
           {
             model: OrderProductItem,
             as: 'productItems',
             include: [{
               model: Product,
               as: 'product',
               include: [{
                 model: ProductFeature,
                 as: 'features',
                 through: {
                   model: ProductFeatureRelation,
                   attributes: ['feature_price'] // 包含功能价格
                 }
               }]
             }]
           },
           { model: OrderServiceItem, as: 'serviceItems' },
           { model: OrderAttachment, as: 'attachments', include: [{ model: Employee, as: 'uploader', attributes: ['name'] }] },
           { model: Employee, as: 'creator' }
       ]
   };

   if (transaction) {
       options.transaction = transaction;
   }

   const order = await OrderHead.findByPk(id, options);
   if (!order) {
       throw new Error('订单未找到');
   }



   return order;
};

/**
 * 计算订单佣金
 * @param {string} partnerUserId - 合伙人用户ID
 * @param {number} actualAmount - 订单实付金额
 * @returns {object} 包含佣金信息的对象
 */
const calculateCommission = async (partnerUserId, actualAmount) => {
    if (!partnerUserId || !actualAmount) {
        return {
            commission_base: null,
            commission_extra: null,
            commission_amount: 0
        };
    }

    // 根据合伙人用户ID查找用户信息
    const partner = await User.findOne({
        where: { id: partnerUserId }
    });

    if (!partner || !partner.is_partner) {
        return {
            commission_base: null,
            commission_extra: null,
            commission_amount: 0
        };
    }

    const commissionBase = parseFloat(partner.commission_base || 0);
    const commissionExtra = parseFloat(partner.commission_extra || 0);
    const totalCommissionRate = commissionBase + commissionExtra;
    const commissionAmount = parseFloat(actualAmount) * totalCommissionRate;

    return {
        commission_base: commissionBase,
        commission_extra: commissionExtra,
        commission_amount: commissionAmount
    };
};

/**
 * 创建产品订单
 * @param {object} orderData - 包含订单信息的对象
 * @param {number} creatorId - 创建人ID
 */
const createProductOrder = async (orderData, creatorId) => {
    return sequelize.transaction(async (t) => {
        // 清理可选外键，防止空字符串导致约束失败
        const cleanOrderData = { ...orderData };
        cleanOrderData.asset_id = (cleanOrderData.asset_id === '' || cleanOrderData.asset_id === undefined) ? null : cleanOrderData.asset_id;
        cleanOrderData.enterprise_id = (cleanOrderData.enterprise_id === '' || cleanOrderData.enterprise_id === undefined) ? null : cleanOrderData.enterprise_id;
        cleanOrderData.partner_user_id = (cleanOrderData.partner_user_id === '' || cleanOrderData.partner_user_id === undefined) ? null : cleanOrderData.partner_user_id;
        cleanOrderData.user_id = (cleanOrderData.user_id === '' || cleanOrderData.user_id === undefined) ? null : cleanOrderData.user_id;



        // 业务规则校验
        if (!cleanOrderData.user_id && !cleanOrderData.enterprise_id) {
            throw new Error('创建订单失败：必须关联一个用户或一个企业。');
        }

        // 设置订单类型
        cleanOrderData.order_category = '产品订单';
        cleanOrderData.creator_id = creatorId;

        // 自动生成或验证 order_id
        if (!cleanOrderData.order_id) {
            cleanOrderData.order_id = await generateOrderId('PO');
        } else {
            const existing = await OrderHead.findOne({ where: { order_id: cleanOrderData.order_id }, transaction: t });
            if (existing) {
                throw new Error(`订单ID '${cleanOrderData.order_id}' 已存在。`);
            }
        }

        // 如果是合伙人订单，处理佣金
        if (cleanOrderData.partner_user_id && cleanOrderData.actual_amount) {
            // 如果前端没有提供佣金比例，则自动获取合伙人默认佣金比例
            if (!cleanOrderData.commission_base && !cleanOrderData.commission_extra) {
                const commissionInfo = await calculateCommission(cleanOrderData.partner_user_id, cleanOrderData.actual_amount);
                cleanOrderData.commission_base = commissionInfo.commission_base;
                cleanOrderData.commission_extra = commissionInfo.commission_extra;
                cleanOrderData.commission_amount = commissionInfo.commission_amount;
            } else {
                // 如果前端提供了佣金比例，则使用前端的值计算佣金金额
                const commissionBase = parseFloat(cleanOrderData.commission_base || 0);
                const commissionExtra = parseFloat(cleanOrderData.commission_extra || 0);
                const totalCommissionRate = commissionBase + commissionExtra;
                cleanOrderData.commission_amount = parseFloat(cleanOrderData.actual_amount) * totalCommissionRate;
            }
            cleanOrderData.is_partner_order = 1;
            cleanOrderData.commission_status = '未发放';
        }

        // 创建订单表头
        const newOrder = await OrderHead.create(cleanOrderData, { transaction: t });

        // 创建产品订单表体
        if (orderData.productItem) {
            const productItemData = {
                ...orderData.productItem,
                order_id: newOrder.id
            };



            // 验证必要字段
            if (!productItemData.product_id) {
                throw new Error('产品ID不能为空');
            }

            await OrderProductItem.create(productItemData, { transaction: t });
        }

        // 返回新创建的、完整的订单信息
        return await getOrderById(newOrder.id, t);
    });
};

/**
 * 创建服务订单
 * @param {object} orderData - 包含订单信息的对象
 * @param {number} creatorId - 创建人ID
 */
const createServiceOrder = async (orderData, creatorId) => {
    return sequelize.transaction(async (t) => {
        // 清理可选外键，防止空字符串导致约束失败
        const cleanOrderData = { ...orderData };
        cleanOrderData.asset_id = (cleanOrderData.asset_id === '' || cleanOrderData.asset_id === undefined) ? null : cleanOrderData.asset_id;
        cleanOrderData.enterprise_id = (cleanOrderData.enterprise_id === '' || cleanOrderData.enterprise_id === undefined) ? null : cleanOrderData.enterprise_id;
        cleanOrderData.partner_user_id = (cleanOrderData.partner_user_id === '' || cleanOrderData.partner_user_id === undefined) ? null : cleanOrderData.partner_user_id;
        cleanOrderData.user_id = (cleanOrderData.user_id === '' || cleanOrderData.user_id === undefined) ? null : cleanOrderData.user_id;



        // 业务规则校验
        if (!cleanOrderData.user_id && !cleanOrderData.enterprise_id) {
            throw new Error('创建订单失败：必须关联一个用户或一个企业。');
        }

        // 设置订单类型
        cleanOrderData.order_category = '服务订单';
        cleanOrderData.creator_id = creatorId;

        // 自动生成或验证 order_id
        if (!cleanOrderData.order_id) {
            cleanOrderData.order_id = await generateOrderId('SO');
        } else {
            const existing = await OrderHead.findOne({ where: { order_id: cleanOrderData.order_id }, transaction: t });
            if (existing) {
                throw new Error(`订单ID '${cleanOrderData.order_id}' 已存在。`);
            }
        }

        // 如果是合伙人订单，处理佣金
        if (cleanOrderData.partner_user_id && cleanOrderData.actual_amount) {
            // 如果前端没有提供佣金比例，则自动获取合伙人默认佣金比例
            if (!cleanOrderData.commission_base && !cleanOrderData.commission_extra) {
                const commissionInfo = await calculateCommission(cleanOrderData.partner_user_id, cleanOrderData.actual_amount);
                cleanOrderData.commission_base = commissionInfo.commission_base;
                cleanOrderData.commission_extra = commissionInfo.commission_extra;
                cleanOrderData.commission_amount = commissionInfo.commission_amount;
            } else {
                // 如果前端提供了佣金比例，则使用前端的值计算佣金金额
                const commissionBase = parseFloat(cleanOrderData.commission_base || 0);
                const commissionExtra = parseFloat(cleanOrderData.commission_extra || 0);
                const totalCommissionRate = commissionBase + commissionExtra;
                cleanOrderData.commission_amount = parseFloat(cleanOrderData.actual_amount) * totalCommissionRate;
            }
            cleanOrderData.is_partner_order = 1;
            cleanOrderData.commission_status = '未发放';
        }

        // 创建订单表头
        const newOrder = await OrderHead.create(cleanOrderData, { transaction: t });

        // 创建服务订单表体
        if (orderData.serviceItems && orderData.serviceItems.length > 0) {
            const serviceItemsData = orderData.serviceItems.map(item => ({
                ...item,
                order_id: newOrder.id
            }));
            await OrderServiceItem.bulkCreate(serviceItemsData, { transaction: t });
        }

        // 返回新创建的、完整的订单信息
        return await getOrderById(newOrder.id, t);
    });
};



/**
 * 更新订单
 * @param {number} id - 订单主键ID
 * @param {object} orderData - 要更新的订单数据
 */
const updateOrder = async (id, orderData) => {
    return sequelize.transaction(async (t) => {
        const orderToUpdate = await OrderHead.findByPk(id, { transaction: t });
        if (!orderToUpdate) {
            throw new Error('订单未找到');
        }

        // 清理可选外键
        const cleanOrderData = { ...orderData };
        cleanOrderData.asset_id = cleanOrderData.asset_id || null;
        cleanOrderData.enterprise_id = cleanOrderData.enterprise_id || null;
        cleanOrderData.partner_user_id = cleanOrderData.partner_user_id || null;
        cleanOrderData.user_id = cleanOrderData.user_id || null;

        await orderToUpdate.update(cleanOrderData, { transaction: t });

        // 返回更新后的完整订单数据
        return await getOrderById(id);
    });
};

/**
 * 审核订单 - 通过审核
 * @param {number} id - 订单主键ID
 */
const approveOrder = async (id) => {
    return sequelize.transaction(async (t) => {
        const order = await OrderHead.findByPk(id, { transaction: t });
        if (!order) {
            throw new Error('订单未找到');
        }

        await order.update({ audit_status: '已审核' }, { transaction: t });
        return await getOrderById(id);
    });
};

/**
 * 审核订单 - 拒绝审核
 * @param {number} id - 订单主键ID
 * @param {string} reason - 拒绝原因
 */
const rejectOrder = async (id, reason) => {
    return sequelize.transaction(async (t) => {
        const order = await OrderHead.findByPk(id, { transaction: t });
        if (!order) {
            throw new Error('订单未找到');
        }

        const updateData = {
            audit_status: '已拒绝'
        };
        if (reason) {
            updateData.remark = reason;
        }

        await order.update(updateData, { transaction: t });
        return await getOrderById(id);
    });
};

/**
 * 删除订单
 * @param {number} id - 订单主键ID
 */
const deleteOrder = async (id) => {
    return sequelize.transaction(async (t) => {
        // 先检查订单是否存在
        const order = await OrderHead.findByPk(id, { transaction: t });
        if (!order) {
            throw new Error("订单未找到");
        }

        // 删除关联的订单明细
        await OrderProductItem.destroy({
            where: { order_id: id },
            transaction: t
        });

        await OrderServiceItem.destroy({
            where: { order_id: id },
            transaction: t
        });

        // 删除关联的附件
        await OrderAttachment.destroy({
            where: { order_id: id },
            transaction: t
        });

        // 最后删除订单头
        const deleted = await OrderHead.destroy({
            where: { id: id },
            transaction: t
        });

        if (!deleted) {
            throw new Error("订单删除失败");
        }

        return { message: "订单删除成功" };
    });
};

/**
 * 获取用户订单列表 - 用于小程序
 * 支持根据企业ID过滤，显示用户相关的所有订单（不仅仅是合伙人订单）
 * @param {string} userId - 用户ID
 * @param {object} options - 查询选项
 */
const getUserOrders = async (userId, options = {}) => {
    const { page = 1, pageSize = 100, enterprise_id } = options;

    // 验证用户是否存在
    const user = await User.findByPk(userId);
    if (!user) {
        throw new Error('用户不存在');
    }

    // 构建查询条件
    const whereConditions = {
        [Op.or]: [
            { user_id: userId },  // 用户直接关联的订单
            { partner_user_id: userId }  // 用户作为合伙人的订单
        ]
    };

    // 如果指定了企业ID，添加企业过滤
    if (enterprise_id) {
        whereConditions.enterprise_id = enterprise_id;
    }

    // 暂时显示所有状态的订单，后续可根据业务需求调整
    // whereConditions.audit_status = '已审核';

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    const { count, rows: orders } = await OrderHead.findAndCountAll({
        where: whereConditions,
        include: [
            {
                model: Enterprise,
                as: 'enterprise',
                attributes: ['id', 'name', 'enterprise_id']
            },
            {
                model: Asset,
                as: 'asset',
                include: [
                    {
                        model: Product,
                        as: 'product',
                        attributes: ['id', 'product_name']
                    }
                ]
            },
            {
                model: OrderProductItem,
                as: 'productItems',
                include: [
                    {
                        model: Product,
                        as: 'product',
                        attributes: ['id', 'product_name', 'version_name']
                    }
                ]
            },
            {
                model: OrderServiceItem,
                as: 'serviceItems',
                attributes: ['id', 'service_name', 'standard_price', 'actual_price']
            }
        ],
        order: [['created_at', 'DESC']],
        offset,
        limit
    });

    // 格式化订单数据
    const formattedOrders = orders.map(order => {
        // 获取产品信息
        let productInfo = '';
        if (order.productItems && order.productItems.length > 0) {
            const productItem = order.productItems[0];
            if (productItem.product) {
                productInfo = `${productItem.product.product_name}${productItem.product.version_name ? ` ${productItem.product.version_name}` : ''}`;
            }
        }

        // 获取服务信息
        let serviceInfo = [];
        if (order.serviceItems && order.serviceItems.length > 0) {
            serviceInfo = order.serviceItems.map(item => ({
                service_name: item.service_name,
                standard_price: item.standard_price,
                actual_price: item.actual_price
            }));
        }

        return {
            order_id: order.order_id,
            order_category: order.order_category,
            product_name: productInfo,
            company_name: order.enterprise?.name || '未知公司',
            actual_amount: order.actual_amount,
            commission_amount: order.commission_amount,
            commission_status: order.commission_status,
            payment_status: order.payment_status,
            created_at: order.created_at,
            // 新增字段用于前端判断和显示
            productItems: order.productItems || [],
            serviceItems: serviceInfo,
            ...order.toJSON()
        };
    });

    return {
        orders: formattedOrders,
        total: count,
        page: parseInt(page),
        pageSize: limit
    };
};

/**
 * 获取合伙人订单列表
 * @param {string} userId - 用户ID
 * @param {object} options - 查询选项
 */
const getPartnerOrders = async (userId, options = {}) => {
    const { page = 1, pageSize = 100 } = options;

    // 验证用户是否为合伙人
    const user = await User.findByPk(userId);
    if (!user || !user.is_partner) {
        throw new Error('您不是合伙人，无法查看订单信息');
    }

    // 查询合伙人相关的订单
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    const { count, rows: orders } = await OrderHead.findAndCountAll({
        where: {
            partner_user_id: user.id,  // 使用user.id而不是user.user_id
            is_partner_order: 1
        },
        include: [
            {
                model: Enterprise,
                as: 'enterprise',
                attributes: ['id', 'name', 'enterprise_id']
            },
            {
                model: Asset,
                as: 'asset',
                include: [
                    {
                        model: Product,
                        as: 'product',
                        attributes: ['id', 'product_name']
                    }
                ]
            },
            {
                model: OrderProductItem,
                as: 'productItems',
                include: [
                    {
                        model: Product,
                        as: 'product',
                        attributes: ['id', 'product_name', 'version_name']
                    }
                ]
            },
            {
                model: OrderServiceItem,
                as: 'serviceItems',
                attributes: ['id', 'service_name', 'standard_price', 'actual_price']
            }
        ],
        order: [['created_at', 'DESC']],
        offset,
        limit
    });

    // 格式化订单数据
    const formattedOrders = orders.map(order => {
        let productInfo = '';
        let serviceInfo = [];

        // 根据订单类型处理产品/服务信息
        if (order.order_category === '产品订单') {
            // 产品订单：显示产品-版本信息
            if (order.productItems && order.productItems.length > 0) {
                const productItem = order.productItems[0]; // 产品订单只有一个产品
                const productName = productItem.product?.product_name || '未知产品';
                const version = productItem.product?.version_name || '';
                productInfo = version ? `${productName}-${version}` : productName;
            } else if (order.asset?.product) {
                // 备用方案：从asset获取产品信息
                const productName = order.asset.product.product_name || '未知产品';
                productInfo = productName;
            }
        } else if (order.order_category === '服务订单') {
            // 服务订单：获取所有服务项目
            if (order.serviceItems && order.serviceItems.length > 0) {
                serviceInfo = order.serviceItems.map(item => ({
                    service_name: item.service_name,
                    standard_price: item.standard_price,
                    actual_price: item.actual_price
                }));
                // 为了兼容现有显示逻辑，也设置productInfo为服务列表的简要描述
                productInfo = order.serviceItems.map(item => item.service_name).join('、');
            }
        }

        return {
            order_id: order.order_id,
            order_category: order.order_category,
            product_name: productInfo,
            company_name: order.enterprise?.name || '未知公司',
            actual_amount: order.actual_amount,
            commission_amount: order.commission_amount,
            commission_status: order.commission_status,
            payment_status: order.payment_status,
            created_at: order.created_at,
            // 新增字段用于前端判断和显示
            productItems: order.productItems || [],
            serviceItems: serviceInfo,
            ...order.toJSON()
        };
    });

    return {
        orders: formattedOrders,
        total: count,
        page: parseInt(page),
        pageSize: limit
    };
};


module.exports = {
    getOrdersForReview,
    getProductOrders,
    getServiceOrders,
    getOrderById,
    createProductOrder,
    createServiceOrder,
    updateOrder,
    approveOrder,
    rejectOrder,
    deleteOrder,
    getUserOrders,
    getPartnerOrders
};