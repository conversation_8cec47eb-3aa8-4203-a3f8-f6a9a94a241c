/**
 * 合伙人小程序API请求封装
 * 统一管理所有与后端的接口通信
 * 与商城小程序保持一致的API调用方式
 */

// 定义后端服务的基础URL - 与商城小程序保持一致
// 生产环境：使用HTTPS域名
const BASE_URL = 'https://service.bogoo.net/api';
// 开发环境：使用服务器IP（需要在开发者工具中关闭域名校验）
// const BASE_URL = 'http://*************:3002/api';

/**
 * 统一处理API响应数据格式
 * 支持多种常见的响应格式，统一转换为数组或对象
 * @param {*} response - API响应数据
 * @param {string} type - 期望的数据类型 'array' | 'object'
 * @returns {Array|Object} 处理后的数据
 */
const normalizeApiResponse = (response, type = 'array') => {
  if (!response) {
    return type === 'array' ? [] : {};
  }

  if (type === 'array') {
    // 处理数组类型的响应
    if (Array.isArray(response)) {
      return response;
    } else if (response.records && Array.isArray(response.records)) {
      // 分页格式: {records: [], total: x, page: x, pageSize: x}
      return response.records;
    } else if (response.data && Array.isArray(response.data)) {
      // 标准格式: {data: []}
      return response.data;
    } else if (response.success && response.data && Array.isArray(response.data)) {
      // 成功格式: {success: true, data: []}
      return response.data;
    } else {
      console.warn('API响应格式不符合预期，期望数组类型:', response);
      return [];
    }
  } else {
    // 处理对象类型的响应
    if (response.data && typeof response.data === 'object') {
      return response.data;
    } else if (response.success && response.data && typeof response.data === 'object') {
      return response.data;
    } else if (typeof response === 'object' && !Array.isArray(response)) {
      return response;
    } else {
      console.warn('API响应格式不符合预期，期望对象类型:', response);
      return {};
    }
  }
};

/**
 * 统一的HTTP请求方法
 * @param {string} url - 请求地址
 * @param {object} options - 请求配置
 * @returns {Promise} 返回Promise对象
 */
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    // 显示加载中提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 打印详细请求信息用于调试
    console.log(`合伙人小程序发起请求: ${BASE_URL}${url}`, options);

    // 确保 options 是对象
    if (typeof options !== 'object') {
      options = {};
    }

    // 初始化请求头
    if (!options.header) {
      options.header = {};
    }

    // 获取存储的token并添加到请求头
    // 除了登录接口外，所有请求都需要携带token
    if (!url.includes('/auth/password-login') && !url.includes('/auth/phone-login')) {
      const token = wx.getStorageSync('access_token');
      if (token) {
        console.log('自动添加token到请求头:', token);
        options.header['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn('请求需要token但未找到有效token');
      }
    }

    // 发起微信小程序请求
    wx.request({
      url: `${BASE_URL}${url}`,
      method: options.method || 'GET',
      data: options.data,
      timeout: 10000, // 设置10秒超时
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        console.log(`合伙人小程序请求成功: ${url}`, res);
        console.log('HTTP状态码:', res.statusCode);
        console.log('响应数据:', res.data);

        // 检查HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 成功响应，直接返回数据
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 处理认证失败
          if (url.includes('/auth/password-login') || url.includes('/auth/phone-login')) {
            // 登录接口的401错误，返回详细错误信息
            resolve(res.data);
          } else {
            // 其他接口的401错误，说明token过期
            wx.removeStorageSync('access_token');
            wx.removeStorageSync('user_info');
            wx.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
              duration: 2000
            });
            reject(new Error('登录已过期'));
          }
        } else {
          // 其他HTTP错误
          const errorMessage = res.data?.message || `请求失败，状态码：${res.statusCode}`;
          console.error('HTTP错误:', errorMessage);
          reject(new Error(errorMessage));
        }
      },
      // 网络请求失败的回调
      fail: (error) => {
        console.error('合伙人小程序请求失败:', error);
        if (error.errMsg && error.errMsg.includes('timeout')) {
          reject(new Error('请求超时，请检查网络连接'));
        } else {
          reject(new Error(error.errMsg || '网络请求失败'));
        }
      },
      complete: () => {
        // 隐藏加载提示
        wx.hideLoading();
      }
    });
  });
};

/**
 * 工具方法：保存用户登录信息到本地存储
 * @param {Object} loginData - 登录返回的数据
 */
function saveLoginInfo(loginData) {
  // 支持两种token字段名：token 和 access_token
  const token = loginData.token || loginData.access_token;
  if (token) {
    wx.setStorageSync('access_token', token);
  }
  if (loginData.refresh_token) {
    wx.setStorageSync('refresh_token', loginData.refresh_token);
  }
  if (loginData.user) {
    wx.setStorageSync('user_info', loginData.user);
  }
}

/**
 * 工具方法：清除用户登录信息
 */
function clearLoginInfo() {
  wx.removeStorageSync('access_token');
  wx.removeStorageSync('refresh_token');
  wx.removeStorageSync('user_info');
}

/**
 * 工具方法：检查是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = wx.getStorageSync('access_token');
  return !!token;
}

/**
 * 工具方法：获取本地存储的用户信息
 * @returns {Object|null} 用户信息
 */
function getLocalUserInfo() {
  return wx.getStorageSync('user_info') || null;
}

/**
 * 统一的登录状态和合伙人权限检查方法
 * @param {Object} options - 配置选项
 * @param {boolean} options.showModal - 是否显示提示弹窗，默认true
 * @param {boolean} options.redirectToLogin - 是否重定向到登录页，默认true
 * @returns {boolean} 检查是否通过
 */
function checkPartnerAuth(options = {}) {
  const { showModal = true, redirectToLogin = true } = options;

  const token = wx.getStorageSync('access_token');
  const userInfo = wx.getStorageSync('user_info');

  // 检查是否已登录
  if (!token) {
    if (showModal) {
      wx.showModal({
        title: '未登录',
        content: '请先登录',
        showCancel: false,
        success: () => {
          if (redirectToLogin) {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        }
      });
    } else if (redirectToLogin) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }
    return false;
  }

  // 检查是否为合伙人
  // 检查多种可能的合伙人标识字段
  const isPartner = userInfo && (userInfo.is_partner === 1 || userInfo.is_partner === true || userInfo.is_partner === '1');

  if (!isPartner) {
    if (showModal) {
      wx.showModal({
        title: '权限不足',
        content: '您不是合伙人，无法访问此页面',
        showCancel: false,
        success: () => {
          if (redirectToLogin) {
            clearLoginInfo();
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        }
      });
    } else if (redirectToLogin) {
      clearLoginInfo();
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }
    return false;
  }

  return true;
}

// 导出所有API方法
module.exports = {
  // ==================== 合伙人认证相关API ====================

  /**
   * 合伙人登录（使用账号密码登录）
   * @param {Object} data - 登录数据 {username, password}
   * @returns {Promise} 登录结果
   */
  partnerLogin: (data) => {
    return request('/auth/password-login', {
      method: 'POST',
      data: {
        username: data.phone || data.username, // 兼容手机号登录
        password: data.password
      }
    });
  },

  /**
   * 合伙人微信手机号快速验证登录
   * @param {Object} data - 登录数据 {code}
   * @returns {Promise} 登录结果
   */
  partnerWechatPhoneLogin: (data) => {
    return request('/auth/phone-login', {
      method: 'POST',
      data: {
        code: data.code,
        loginType: 'phone_verification'
      }
    });
  },

  /**
   * 获取合伙人收益统计
   * @returns {Promise} 收益数据
   */
  getPartnerEarnings: () => {
    return request('/auth/partner/earnings', {
      method: 'GET'
    });
  },

  /**
   * 获取当前合伙人用户信息
   * @returns {Promise} 用户信息
   */
  getCurrentUser: () => {
    return request('/auth/me', {
      method: 'GET'
    });
  },

  /**
   * 修改密码
   * @param {Object} data - 密码数据 {oldPassword, newPassword}
   * @returns {Promise} 修改结果
   */
  changePassword: (data) => {
    return request('/auth/change-password', {
      method: 'PUT',
      data: {
        oldPassword: data.oldPassword,
        newPassword: data.newPassword
      }
    });
  },

  /**
   * 合伙人注册申请
   * @param {Object} data - 注册数据
   * @returns {Promise} 注册结果
   */
  registerPartner: (data) => {
    return request('/auth/partner-register', {
      method: 'POST',
      data
    });
  },

  /**
   * 获取认证信息
   * @returns {Promise} 认证信息
   */
  getAuthInfo: () => {
    return request('/user/auth-info', {
      method: 'GET'
    });
  },

  /**
   * 更新认证信息
   * @param {Object} data - 认证信息数据
   * @returns {Promise} 更新结果
   */
  updateAuthInfo: (data) => {
    return request('/user/auth-info', {
      method: 'PUT',
      data
    });
  },

  // ==================== 订单相关API ====================

  /**
   * 获取合伙人相关的订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 订单列表
   */
  getPartnerOrders: (params = {}) => {
    // 构建查询字符串
    const queryString = Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    const url = queryString ? `/orders?${queryString}` : '/orders';

    return request(url, {
      method: 'GET'
    });
  },

  /**
   * 获取订单详情
   * @param {number} orderId - 订单ID
   * @returns {Promise} 订单详情
   */
  getOrderDetail: (orderId) => {
    return request(`/orders/${orderId}`, {
      method: 'GET'
    });
  },

  // ==================== 产品相关API ====================

  /**
   * 获取产品列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 产品列表
   */
  getProductsList: (params = {}) => {
    const queryString = Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    const url = queryString ? `/products?${queryString}` : '/products';

    return request(url, {
      method: 'GET'
    });
  },

  /**
   * 获取产品详情
   * @param {number} productId - 产品ID
   * @returns {Promise} 产品详情
   */
  getProductDetail: (productId) => {
    return request(`/products/${productId}`, {
      method: 'GET'
    });
  },

  // ==================== 工具方法 ====================

  // 保存用户登录信息到本地存储
  saveLoginInfo,

  // 清除用户登录信息
  clearLoginInfo,

  // 检查是否已登录
  isLoggedIn,

  // 获取本地存储的用户信息
  getLocalUserInfo,

  // 统一的合伙人权限检查
  checkPartnerAuth,

  // 基础请求方法
  request,

  // 工具方法
  normalizeApiResponse,

  // 常量
  BASE_URL
};