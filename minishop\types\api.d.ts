/**
 * API响应类型定义
 * 用于统一前端API调用的数据结构
 */

// 基础API响应格式
export interface BaseApiResponse<T = any> {
  success?: boolean;
  message?: string;
  data?: T;
}

// 分页响应格式
export interface PaginatedApiResponse<T = any> {
  records: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 资产相关类型
export interface Asset {
  id: number;
  name: string;
  product_name: string;
  product_expiry_date: string;
  sps_expiry_date: string;
  status: string;
  enterprise_id: number;
  // 计算字段
  productExpiryStatus?: 'active' | 'warning' | 'expired';
  spsExpiryStatus?: 'active' | 'warning' | 'expired';
}

// 订单相关类型
export interface Order {
  id: number;
  order_number: string;
  product_name: string;
  amount: number;
  payment_status: '待付款' | '已付款' | 'pending' | 'paid';
  created_at: string;
  enterprise_id: number;
}

// 企业相关类型
export interface Enterprise {
  id: number;
  name: string;
  contact_person: string;
  contact_phone: string;
  status: string;
}

// 用户信息类型
export interface UserInfo {
  id: number;
  username: string;
  email: string;
  role: string;
  isLoggedIn: boolean;
  nickName: string;
  companyName: string;
  displayUserId: string;
}

// API响应的联合类型
export type ApiResponse<T> = 
  | T[]  // 直接数组
  | BaseApiResponse<T[]>  // {success: true, data: []}
  | PaginatedApiResponse<T>  // {records: [], total: x, page: x, pageSize: x}
  | BaseApiResponse<T>;  // {success: true, data: {}}

// API方法类型定义
export interface ApiService {
  // 基础HTTP方法
  get<T = any>(url: string, params?: Record<string, any>): Promise<T>;
  post<T = any>(url: string, data?: any): Promise<T>;
  put<T = any>(url: string, data?: any): Promise<T>;
  del<T = any>(url: string): Promise<T>;
  
  // 响应处理工具
  normalizeApiResponse<T>(response: ApiResponse<T>, type: 'array'): T[];
  normalizeApiResponse<T>(response: ApiResponse<T>, type: 'object'): T;
  
  // 资产相关
  getAssetsList(params?: { enterpriseId?: number; q?: string; page?: number; pageSize?: number }): Promise<ApiResponse<Asset>>;
  getAssetDetail(assetId: number): Promise<ApiResponse<Asset>>;
  
  // 订单相关
  getOrdersList(params?: { enterprise_id?: number; status?: string }): Promise<ApiResponse<Order>>;
  getOrderDetail(orderId: number): Promise<ApiResponse<Order>>;
  
  // 企业相关
  getEnterprisesList(): Promise<ApiResponse<Enterprise>>;
  
  // 用户相关
  getCurrentUser(): Promise<ApiResponse<UserInfo>>;
  isLoggedIn(): boolean;
  getLocalUserInfo(): UserInfo | null;
}

// 小程序页面数据类型
export interface MePageData {
  userInfo: UserInfo;
  enterprises: Enterprise[];
  currentEnterprise: Enterprise;
  assets: Asset[];
  activeAssets: Asset[];
  expiredAssets: Asset[];
  filteredAssets: Asset[];
  orders: Order[];
  pendingOrders: Order[];
  paidOrders: Order[];
  filteredOrders: Order[];
  loading: {
    assets: boolean;
    orders: boolean;
  };
}

// 错误处理类型
export interface ApiError {
  message: string;
  code?: number;
  details?: any;
}
