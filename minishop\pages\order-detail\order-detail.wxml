<!--pages/order-detail/order-detail.wxml-->
<wxs module="utils">
  // 格式化价格
  function formatPrice(price) {
    if (!price) return '0.00';
    return parseFloat(price).toFixed(2);
  }

  // 格式化日期
  function formatDate(dateStr) {
    if (!dateStr) return '';
    try {
      var date = getDate(dateStr);
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      var hour = date.getHours();
      var minute = date.getMinutes();

      // 手动补零，因为WXS不支持padStart
      var monthStr = month < 10 ? '0' + month : month.toString();
      var dayStr = day < 10 ? '0' + day : day.toString();
      var hourStr = hour < 10 ? '0' + hour : hour.toString();
      var minuteStr = minute < 10 ? '0' + minute : minute.toString();

      return year + '-' + monthStr + '-' + dayStr + ' ' + hourStr + ':' + minuteStr;
    } catch (e) {
      return dateStr;
    }
  }

  // 格式化简短日期
  function formatDateShort(dateStr) {
    if (!dateStr) return '';
    try {
      var date = getDate(dateStr);
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();

      // 手动补零，因为WXS不支持padStart
      var monthStr = month < 10 ? '0' + month : month.toString();
      var dayStr = day < 10 ? '0' + day : day.toString();

      return year + '-' + monthStr + '-' + dayStr;
    } catch (e) {
      return dateStr;
    }
  }

  // 获取支付状态样式
  function getPaymentStatusClass(status) {
    switch(status) {
      case '待支付': return 'pending';
      case '已支付': return 'paid';
      default: return 'default';
    }
  }

  // 获取审核状态样式
  function getAuditStatusClass(status) {
    switch(status) {
      case '待审核': return 'pending';
      case '已审核': return 'approved';
      case '已拒绝': return 'rejected';
      default: return 'default';
    }
  }

  // 格式化折扣率
  function formatDiscountRate(rate) {
    if (!rate) return '0.0';
    return (parseFloat(rate) * 100).toFixed(1);
  }

  // 获取订单类型图标
  function getOrderTypeIcon(category) {
    switch(category) {
      case '产品订单': return '📦';
      case '服务订单': return '🛠️';
      default: return '📋';
    }
  }

  // 检查功能是否被选中
  function isFeatureSelected(featureId, selectedFeatures) {
    if (!selectedFeatures || !featureId) {
      return false;
    }

    // 处理不同的数据格式
    var features = selectedFeatures;

    // 如果是字符串，先尝试按逗号分割（处理 "3,5" 这种格式）
    if (typeof selectedFeatures === 'string') {
      // 检查是否是逗号分隔的字符串
      if (selectedFeatures.indexOf(',') !== -1) {
        features = selectedFeatures.split(',');
        // 转换为数字数组
        for (var j = 0; j < features.length; j++) {
          features[j] = parseInt(features[j].trim());
        }
      } else {
        // 尝试解析为JSON
        try {
          features = JSON.parse(selectedFeatures);
        } catch (e) {
          // 如果解析失败，可能是单个值的字符串
          return selectedFeatures.toString() === featureId.toString();
        }
      }
    }

    // 如果不是数组，返回false
    if (!Array.isArray(features)) {
      return false;
    }

    // 遍历数组查找匹配的功能ID
    for (var i = 0; i < features.length; i++) {
      // 支持数字和字符串比较
      if (features[i] == featureId || features[i].toString() == featureId.toString()) {
        return true;
      }
    }

    return false;
  }

  // 获取文件类型图标
  function getFileTypeIcon(fileType) {
    if (!fileType) return '📄';
    var type = fileType.toLowerCase();
    if (type.indexOf('pdf') >= 0) return '📄';
    if (type.indexOf('doc') >= 0 || type.indexOf('word') >= 0) return '📝';
    if (type.indexOf('xls') >= 0 || type.indexOf('excel') >= 0) return '📊';
    if (type.indexOf('image') >= 0 || type.indexOf('jpg') >= 0 || type.indexOf('png') >= 0) return '🖼️';
    if (type.indexOf('zip') >= 0 || type.indexOf('rar') >= 0) return '📦';
    return '📄';
  }

  // 获取文件类型标签样式
  function getFileTypeClass(fileType) {
    if (!fileType) return 'default';
    var type = fileType.toLowerCase();
    if (type.indexOf('pdf') >= 0) return 'pdf';
    if (type.indexOf('doc') >= 0 || type.indexOf('word') >= 0) return 'doc';
    if (type.indexOf('xls') >= 0 || type.indexOf('excel') >= 0) return 'excel';
    if (type.indexOf('image') >= 0 || type.indexOf('jpg') >= 0 || type.indexOf('png') >= 0) return 'image';
    return 'default';
  }



  // 格式化文件大小
  function formatFileSize(bytes) {
    if (!bytes) return '';
    if (bytes < 1024) return bytes + 'B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + 'KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + 'MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + 'GB';
  }

  module.exports = {
    formatPrice: formatPrice,
    formatDate: formatDate,
    formatDateShort: formatDateShort,
    getPaymentStatusClass: getPaymentStatusClass,
    getAuditStatusClass: getAuditStatusClass,
    getOrderTypeIcon: getOrderTypeIcon,
    formatDiscountRate: formatDiscountRate,
    isFeatureSelected: isFeatureSelected,
    getFileTypeIcon: getFileTypeIcon,
    getFileTypeClass: getFileTypeClass,
    formatFileSize: formatFileSize
  };
</wxs>

<view class="page-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="navbar-title">订单详情</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <view class="error-text">{{error}}</view>
    <button class="retry-btn" bindtap="loadOrderDetail">重试</button>
  </view>

  <!-- 订单详情内容 -->
  <view wx:else class="content-container">
    <!-- 订单概览卡片（合并表头和基本信息） -->
    <view class="info-card overview-card">
      <view class="order-header">
        <view class="order-main">
          <view class="order-title-line">
            <text class="order-id">{{order.order_id}}</text>
            <text class="order-category">{{order.order_category}}</text>
          </view>
          <view class="order-enterprise" wx:if="{{order.enterprise}}">{{order.enterprise.name}}</view>
        </view>
        <view class="order-amount">¥{{utils.formatPrice(order.actual_amount)}}</view>
      </view>

      <view class="order-meta">
        <view class="payment-status-tag {{utils.getPaymentStatusClass(order.payment_status)}}">
          {{order.payment_status}}
        </view>
        <view class="order-date">{{utils.formatDateShort(order.created_at)}}</view>
      </view>

      <!-- 基本信息集成到概览卡片中 -->
      <view class="basic-info-section">
        <view class="info-row">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{utils.formatDate(order.created_at)}}</text>
        </view>

        <view class="info-row" wx:if="{{order.order_type}}">
          <text class="info-label">订单类型</text>
          <text class="info-value order-type-tag">{{order.order_type}}</text>
        </view>

        <view class="info-row" wx:if="{{order.payment_method}}">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{order.payment_method}}</text>
        </view>

        <view class="info-row" wx:if="{{order.payment_time}}">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{utils.formatDate(order.payment_time)}}</text>
        </view>
      </view>
    </view>

    <!-- 产品订单明细 -->
    <view class="info-card" wx:if="{{order.productItems && order.productItems.length > 0}}">
      <view class="card-title">产品明细</view>

      <view class="product-list">
        <view wx:for="{{order.productItems}}" wx:key="id" class="product-item-new">
          <view class="product-header-new">
            <view class="product-title">
              <text class="product-name">{{item.product ? item.product.product_name : '未知产品'}}</text>
              <text class="product-version-tag" wx:if="{{item.product && item.product.version_name}}">{{item.product.version_name}}</text>
            </view>
          </view>

          <view class="product-details-grid">
            <view class="detail-row">
              <text class="detail-label">规格配置</text>
              <view class="product-specs-new">
                <text class="spec-tag">{{item.user_count}}用户</text>
                <text class="spec-tag">{{item.account_count}}账套</text>
                <text class="spec-tag">{{item.duration_months}}个月</text>
              </view>
            </view>

          <!-- 产品功能标签显示 -->
          <view class="purchased-features-section" wx:if="{{item.selectedFeaturesList && item.selectedFeaturesList.length > 0}}">
            <view class="detail-row">
              <text class="detail-label">已购功能</text>
            </view>
            <view class="purchased-features">
              <view wx:for="{{item.selectedFeaturesList}}" wx:for-item="feature" wx:key="id"
                    class="feature-tag purchased">
                <text class="feature-name">{{feature.feature_name}}</text>
                <text class="feature-price" wx:if="{{feature.ProductFeatureRelation && feature.ProductFeatureRelation.feature_price}}">¥{{utils.formatPrice(feature.ProductFeatureRelation.feature_price)}}</text>
              </view>
            </view>
          </view>

          <!-- 如果没有选择功能，显示提示 -->
          <view class="detail-row" wx:elif="{{item.product && item.product.features && item.product.features.length > 0 && (!item.selectedFeaturesList || item.selectedFeaturesList.length === 0)}}">
            <text class="detail-label">已购功能</text>
            <text class="detail-value no-features">未选择功能</text>
          </view>

            <view class="detail-row">
              <text class="detail-label">标准价格</text>
              <text class="detail-value">¥{{utils.formatPrice(item.standard_price)}}</text>
            </view>

            <view class="detail-row" wx:if="{{item.discount_rate && item.discount_rate < 1}}">
              <text class="detail-label">优惠折扣</text>
              <text class="detail-value discount-value">{{utils.formatDiscountRate(item.discount_rate)}}折</text>
            </view>

            <view class="detail-row" wx:if="{{item.other_discount && item.other_discount > 0}}">
              <text class="detail-label">其他优惠</text>
              <text class="detail-value discount-amount">-¥{{utils.formatPrice(item.other_discount)}}</text>
            </view>

            <view class="detail-row">
              <text class="detail-label">实付价格</text>
              <text class="detail-value price-highlight">¥{{utils.formatPrice(item.actual_price)}}</text>
            </view>

            <view class="detail-row" wx:if="{{item.activity_other}}">
              <text class="detail-label">活动说明</text>
              <text class="detail-value activity-desc">{{item.activity_other}}</text>
            </view>
          </view>


        </view>
      </view>
    </view>

    <!-- 服务订单明细 -->
    <view class="info-card" wx:if="{{order.serviceItems && order.serviceItems.length > 0}}">
      <view class="card-title">服务明细</view>

      <view class="service-list">
        <view wx:for="{{order.serviceItems}}" wx:key="id" class="service-item">
          <view class="service-header">
            <view class="service-name">{{item.service_name}}</view>
            <view class="service-price">¥{{utils.formatPrice(item.actual_price)}}</view>
          </view>

          <view class="service-remark" wx:if="{{item.remark}}">
            <text class="remark-text">{{item.remark}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="info-card">
      <view class="card-title">费用明细</view>

      <view class="price-summary-new">
        <view class="summary-row">
          <text class="summary-label">标准价格</text>
          <text class="summary-value">¥{{utils.formatPrice(order.standard_amount)}}</text>
        </view>

        <view class="summary-row">
          <text class="summary-label">实付价格</text>
          <text class="summary-value price-highlight">¥{{utils.formatPrice(order.actual_amount)}}</text>
        </view>

        <view class="summary-row" wx:if="{{order.tax_amount && order.tax_amount > 0}}">
          <text class="summary-label">税额</text>
          <text class="summary-value">¥{{utils.formatPrice(order.tax_amount)}}</text>
        </view>

        <view class="summary-row" wx:if="{{order.invoice_type}}">
          <text class="summary-label">发票类型</text>
          <text class="summary-value">{{order.invoice_type}}</text>
        </view>
      </view>
    </view>

    <!-- 关联资产信息 -->
    <view class="info-card" wx:if="{{order.asset}}">
      <view class="card-title">关联资产</view>

      <view class="asset-info-enhanced" bindtap="viewAsset" data-asset-id="{{order.asset.id}}">
        <view class="asset-header-section">
          <view class="asset-title-row">
            <text class="asset-id-text">{{order.asset.asset_id}}</text>
            <text class="asset-name-text">{{order.asset.product ? order.asset.product.product_name : '未知产品'}}</text>
            <view class="asset-status-tag {{order.asset.status === '在线' ? 'online' : 'offline'}}">
              {{order.asset.status}}
            </view>
          </view>
          <view class="asset-company" wx:if="{{order.asset.enterprise}}">
            {{order.asset.enterprise.name}}
          </view>
        </view>

        <!-- 资产详细信息 -->
        <view class="asset-details-section">
          <view class="asset-detail-row">
            <text class="detail-label">资产编号</text>
            <text class="detail-value asset-id-copy" bindtap="copyAssetId" data-text="{{order.asset.asset_id}}">{{order.asset.asset_id}}</text>
          </view>

          <view class="asset-detail-row" wx:if="{{order.asset.id}}">
            <text class="detail-label">内部ID</text>
            <text class="detail-value">{{order.asset.id}}</text>
          </view>

          <view class="asset-detail-row" wx:if="{{order.asset.product && order.asset.product.version_name}}">
            <text class="detail-label">产品版本</text>
            <text class="detail-value">{{order.asset.product.version_name}}</text>
          </view>

          <view class="asset-detail-row" wx:if="{{order.asset.user_count}}">
            <text class="detail-label">用户数量</text>
            <text class="detail-value">{{order.asset.user_count}}用户</text>
          </view>

          <view class="asset-detail-row" wx:if="{{order.asset.account_count}}">
            <text class="detail-label">账套数量</text>
            <text class="detail-value">{{order.asset.account_count}}账套</text>
          </view>

          <view class="asset-detail-row" wx:if="{{order.asset.purchase_date}}">
            <text class="detail-label">购买时间</text>
            <text class="detail-value">{{utils.formatDateShort(order.asset.purchase_date)}}</text>
          </view>

          <view class="asset-detail-row" wx:if="{{order.asset.product_expiry_date}}">
            <text class="detail-label">产品到期</text>
            <text class="detail-value">{{utils.formatDateShort(order.asset.product_expiry_date)}}</text>
          </view>
        </view>

        <view class="asset-action">
          <text class="view-asset-btn">查看详情 ></text>
        </view>
      </view>
    </view>

    <!-- 附件信息 -->
    <view class="info-card" wx:if="{{order.attachments && order.attachments.length > 0}}">
      <view class="card-title">附件信息</view>

      <view class="attachments-list-enhanced">
        <view wx:for="{{order.attachments}}" wx:key="id" class="attachment-item-enhanced">
          <view class="attachment-header">
            <view class="file-icon">{{utils.getFileTypeIcon(item.file_type)}}</view>
            <view class="attachment-main-info">
              <text class="attachment-name">{{item.filename}}</text>
              <view class="attachment-meta">
                <text class="attachment-type-tag {{utils.getFileTypeClass(item.file_type)}}">{{item.file_type}}</text>
                <text class="attachment-size" wx:if="{{item.file_size}}">{{utils.formatFileSize(item.file_size)}}</text>
                <text class="attachment-uploader" wx:if="{{item.uploader}}">上传人：{{item.uploader.name}}</text>
              </view>
            </view>
          </view>

          <view class="attachment-actions-enhanced">
            <button class="attachment-btn-compact view-btn" bindtap="viewAttachment" data-url="{{item.file_path}}" data-type="{{item.file_type}}">
              查看
            </button>
            <button class="attachment-btn-compact download-btn" bindtap="downloadAttachment" data-url="{{item.file_path}}" data-name="{{item.filename}}">
              ⬇️ 下载
            </button>
          </view>
        </view>
      </view>

      <!-- 如果没有附件，显示提示 -->
      <view wx:if="{{!order.attachments || order.attachments.length === 0}}" class="no-attachments">
        <text class="no-attachments-text">暂无附件</text>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="info-card" wx:if="{{order.remark}}">
      <view class="card-title">备注信息</view>
      <view class="remark-content">{{order.remark}}</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button
        wx:if="{{order.payment_status === '待支付'}}"
        class="action-btn primary"
        bindtap="payOrder"
      >
        立即付款
      </button>

      <button
        class="action-btn secondary"
        bindtap="contactService"
      >
        联系客服
      </button>

      <button
        wx:if="{{order.asset}}"
        class="action-btn secondary"
        bindtap="viewAsset"
        data-asset-id="{{order.asset.id}}"
      >
        <text class="btn-icon">🏷️</text>
        查看资产
      </button>
    </view>
  </view>
  
  <!-- 浮动咨询组件 -->
  <float-consult />
</view>
