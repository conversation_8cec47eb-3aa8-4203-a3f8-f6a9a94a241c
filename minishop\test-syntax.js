// 简单的语法测试
console.log('测试开始...');

// 测试折扣率格式化函数
function formatDiscountRate(rate) {
  if (!rate) return '0.0';
  return (parseFloat(rate) * 100).toFixed(1);
}

// 测试用例
console.log('折扣率测试:');
console.log('0.85 =>', formatDiscountRate(0.85) + '%'); // 应该输出 85.0%
console.log('0.9 =>', formatDiscountRate(0.9) + '%');   // 应该输出 90.0%
console.log('0.75 =>', formatDiscountRate(0.75) + '%'); // 应该输出 75.0%

console.log('语法测试完成！');
