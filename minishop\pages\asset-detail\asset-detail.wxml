<!--pages/asset-detail/asset-detail.wxml-->
<wxs module="utils">
  // 计算剩余天数
  function calculateDaysLeft(expiryDateStr) {
    if (!expiryDateStr) return 0;
    var today = getDate();
    var expiryDate = getDate(expiryDateStr);
    var timeDiff = expiryDate.getTime() - today.getTime();
    return Math.max(0, Math.ceil(timeDiff / (1000 * 3600 * 24)));
  }
  
  // 获取到期状态
  function getExpiryStatus(expiryDateStr) {
    if (!expiryDateStr) {
      return { class: 'normal', text: '无限期' };
    }
    
    var daysLeft = calculateDaysLeft(expiryDateStr);
    
    if (daysLeft <= 0) {
      return { class: 'expired', text: '已过期' };
    } else if (daysLeft <= 30) {
      return { class: 'warning', text: daysLeft + '天后到期' };
    } else {
      return { class: 'normal', text: daysLeft + '天后到期' };
    }
  }
  
  // 格式化价格
  function formatPrice(price) {
    if (!price) return '0.00';
    return parseFloat(price).toFixed(2);
  }

  // 计算总价格
  function calculateTotalPrice(standard, sps, afterSales, implementation) {
    var total = (standard || 0) + (sps || 0) + (afterSales || 0) + (implementation || 0);
    return formatPrice(total);
  }

  // 格式化日期
  function formatDate(dateStr) {
    if (!dateStr) return '';
    try {
      var date = getDate(dateStr);
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();

      // 手动补零，因为WXS不支持padStart
      var monthStr = month < 10 ? '0' + month : month.toString();
      var dayStr = day < 10 ? '0' + day : day.toString();

      return year + '-' + monthStr + '-' + dayStr;
    } catch (e) {
      return dateStr; // 如果解析失败，返回原字符串
    }
  }

  // 获取资产状态样式
  function getAssetStatusClass(status) {
    switch(status) {
      case '在线': return 'online';
      case '过期': return 'expired';
      default: return 'default';
    }
  }

  // 获取资产状态图标
  function getAssetStatusIcon(status) {
    switch(status) {
      case '在线': return '🟢';
      case '过期': return '🔴';
      default: return '⚪';
    }
  }

  // 检查功能是否已购买
  function isFeaturePurchased(featureId, selectedFeatures) {
    if (!selectedFeatures || !featureId) return false;
    // selectedFeatures是一个ID数组
    for (var i = 0; i < selectedFeatures.length; i++) {
      if (selectedFeatures[i] == featureId) {
        return true;
      }
    }
    return false;
  }

  module.exports = {
    getExpiryStatus: getExpiryStatus,
    calculateDaysLeft: calculateDaysLeft,
    formatPrice: formatPrice,
    calculateTotalPrice: calculateTotalPrice,
    formatDate: formatDate,
    getAssetStatusClass: getAssetStatusClass,
    getAssetStatusIcon: getAssetStatusIcon,
    isFeaturePurchased: isFeaturePurchased
  };
</wxs>

<view class="page-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="navbar-title">资产详情</view>
      <view class="navbar-right">
        <text class="nav-icon" bindtap="showMoreActions">⋯</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 资产详情内容 -->
  <view wx:else class="content-container">
    <!-- 资产概览卡片（合并表头和基本信息） -->
    <view class="info-card overview-card">
      <view class="asset-header">
        <view class="asset-main">
          <!-- 资产ID 产品名字 版本号 -->
          <view class="asset-title-line">
            <text class="asset-id-copy" bindtap="copyAssetId" data-text="{{asset.asset_id}}">{{asset.asset_id}}</text>
            <text class="asset-name">{{asset.product ? asset.product.product_name : '未知产品'}}</text>
            <text class="asset-version" wx:if="{{asset.product && asset.product.version_name}}">{{asset.product.version_name}}</text>
          </view>
          <!-- 公司信息 -->
          <view class="asset-company" wx:if="{{asset.enterprise}}">{{asset.enterprise.name}}</view>
        </view>
        <view class="asset-status-tag {{utils.getAssetStatusClass(asset.status)}}">
          {{asset.status}}
        </view>
      </view>

      <view class="asset-specs">
        <text class="spec-text">{{asset.user_count}}用户</text>
        <text class="spec-text">{{asset.account_count}}账套</text>
        <text class="spec-text" wx:if="{{asset.duration_months}}">{{asset.duration_months}}个月</text>
      </view>

      <!-- 购买时间信息 -->
      <view class="basic-info-section">
        <view class="info-row">
          <text class="info-label">购买时间</text>
          <text class="info-value">{{asset.purchase_date ? utils.formatDate(asset.purchase_date) : '未设置'}}</text>
        </view>
      </view>
    </view>

    <!-- 产品功能卡片（已购买功能标签展示） -->
    <view class="info-card" wx:if="{{asset.product && asset.product.features && asset.product.features.length > 0}}">
      <view class="card-title">产品功能</view>

      <view class="purchased-features">
        <view wx:for="{{asset.product.features}}" wx:key="id"
              wx:if="{{utils.isFeaturePurchased(item.id, asset.selected_features)}}"
              class="feature-tag purchased">
          <text class="feature-name">{{item.feature_name}}</text>
          <text class="feature-price" wx:if="{{item.ProductFeatureRelation && item.ProductFeatureRelation.feature_price}}">¥{{utils.formatPrice(item.ProductFeatureRelation.feature_price)}}</text>
        </view>
      </view>

      <!-- 如果没有已购买功能，显示提示 -->
      <view wx:if="{{!asset.selected_features || asset.selected_features.length === 0}}" class="no-features">
        <text class="no-features-text">暂无已购买功能</text>
      </view>
    </view>

    <!-- 到期信息卡片 -->
    <view class="info-card">
      <view class="card-title">到期信息</view>

      <view class="expiry-grid">
        <view class="expiry-item">
          <view class="expiry-content">
            <text class="expiry-label">产品到期</text>
            <text class="expiry-date">{{asset.product_expiry_date ? utils.formatDate(asset.product_expiry_date) : '未设置'}}</text>
          </view>
          <text class="expiry-status {{asset.product_expiry_date ? utils.getExpiryStatus(asset.product_expiry_date).class : 'normal'}}" wx:if="{{asset.product_expiry_date}}">
            {{utils.getExpiryStatus(asset.product_expiry_date).text}}
          </text>
        </view>

        <view class="expiry-item">
          <view class="expiry-content">
            <text class="expiry-label">SPS到期</text>
            <text class="expiry-date">{{asset.sps_expiry_date ? utils.formatDate(asset.sps_expiry_date) : '未设置'}}</text>
          </view>
          <text class="expiry-status {{asset.sps_expiry_date ? utils.getExpiryStatus(asset.sps_expiry_date).class : 'normal'}}" wx:if="{{asset.sps_expiry_date}}">
            {{utils.getExpiryStatus(asset.sps_expiry_date).text}}
          </text>
        </view>

        <view class="expiry-item">
          <view class="expiry-content">
            <text class="expiry-label">服务到期</text>
            <text class="expiry-date">{{asset.after_sales_expiry_date ? utils.formatDate(asset.after_sales_expiry_date) : '未设置'}}</text>
          </view>
          <text class="expiry-status {{asset.after_sales_expiry_date ? utils.getExpiryStatus(asset.after_sales_expiry_date).class : 'normal'}}" wx:if="{{asset.after_sales_expiry_date}}">
            {{utils.getExpiryStatus(asset.after_sales_expiry_date).text}}
          </text>
        </view>
      </view>
    </view>

    <!-- 价格信息卡片 -->
    <view class="info-card" wx:if="{{asset.product_standard_price || asset.sps_annual_fee || asset.after_sales_service_fee || asset.implementation_fee}}">
      <view class="card-title">价格信息</view>

      <view class="price-grid">
        <view class="price-item" wx:if="{{asset.product_standard_price}}">
          <text class="price-label">产品</text>
          <text class="price-value">¥{{utils.formatPrice(asset.product_standard_price)}}</text>
        </view>

        <view class="price-item" wx:if="{{asset.sps_annual_fee}}">
          <text class="price-label">SPS</text>
          <text class="price-value">¥{{utils.formatPrice(asset.sps_annual_fee)}}</text>
        </view>

        <view class="price-item" wx:if="{{asset.after_sales_service_fee}}">
          <text class="price-label">服务</text>
          <text class="price-value">¥{{utils.formatPrice(asset.after_sales_service_fee)}}</text>
        </view>

        <view class="price-item" wx:if="{{asset.implementation_fee}}">
          <text class="price-label">实施</text>
          <text class="price-value">¥{{utils.formatPrice(asset.implementation_fee)}}</text>
        </view>
      </view>

      <view class="price-total">
        <text class="total-label">总计</text>
        <text class="total-value">¥{{utils.calculateTotalPrice(asset.product_standard_price, asset.sps_annual_fee, asset.after_sales_service_fee, asset.implementation_fee)}}</text>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="info-card" wx:if="{{asset.remark}}">
      <view class="card-title">备注信息</view>
      <view class="remark-content">{{asset.remark}}</view>
    </view>

    <!-- 关联订单信息 -->
    <view class="info-card">
      <view class="card-title">
        <text>关联订单</text>
        <text class="toggle-btn" bindtap="toggleOrders">{{showOrders ? '收起' : '展开'}}</text>
      </view>

      <view wx:if="{{showOrders}}" class="orders-section">
        <view class="search-section">
          <text class="search-btn" bindtap="searchOrders">查询更多订单</text>
        </view>

        <view wx:if="{{asset.orders && asset.orders.length > 0}}" class="orders-list">
          <view wx:for="{{asset.orders}}" wx:key="id" class="order-item" bindtap="viewOrder" data-order-id="{{item.id}}">
            <view class="order-header">
              <text class="order-id">{{item.order_id}}</text>
              <text class="order-amount">¥{{utils.formatPrice(item.actual_amount)}}</text>
            </view>
            <view class="order-info">
              <text class="order-type">{{item.order_category}}</text>
              <text class="order-status">{{item.payment_status}}</text>
              <text class="order-date">{{utils.formatDate(item.created_at)}}</text>
            </view>
          </view>
        </view>

        <view wx:else class="no-orders">
          <text class="no-orders-text">暂无关联订单</text>
          <text class="search-tip">点击查询按钮搜索相关订单</text>
        </view>
      </view>

      <view wx:else class="orders-collapsed">
        <text class="collapsed-text">点击展开查看关联订单信息</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="contactService">联系客服</button>
      <button class="action-btn secondary" bindtap="renewAsset">续费资产</button>
    </view>
  </view>

  <!-- 浮动咨询组件 -->
  <float-consult />
</view>
