<template>
  <div class="product-order-list">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h2>产品订单</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增产品订单
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-form :model="filterForm" inline>
        <el-form-item label="订单类型">
          <el-select v-model="filterForm.orderType" placeholder="全部" clearable style="width: 120px">
            <el-option label="普通订单" value="普通订单" />
            <el-option label="续费订单" value="续费订单" />
            <el-option label="变更订单" value="变更订单" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建方式">
          <el-select v-model="filterForm.creationMethod" placeholder="全部" clearable style="width: 120px">
            <el-option label="手工创建" value="手工创建" />
            <el-option label="用户创建" value="用户创建" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select v-model="filterForm.paymentStatus" placeholder="全部" clearable style="width: 120px">
            <el-option label="待支付" value="待支付" />
            <el-option label="已支付" value="已支付" />
          </el-select>
        </el-form-item>
        <el-form-item label="分润状态">
          <el-select v-model="filterForm.commissionStatus" placeholder="全部" clearable style="width: 120px">
            <el-option label="已发放" value="已发放" />
            <el-option label="未发放" value="未发放" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filterForm.orderId" placeholder="输入订单号搜索" style="width: 200px" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">搜索</el-button>
          <el-button @click="handleResetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.totalOrders }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ stats.totalAmount }}</div>
              <div class="stats-label">总金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.paidOrders }}</div>
              <div class="stats-label">已支付订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ stats.commissionAmount }}</div>
              <div class="stats-label">分润金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 产品订单列表表格 -->
    <div class="table-container">
      <el-table 
        :data="filteredOrders" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        show-summary
        :summary-method="getSummaries"
      >
        <!-- 订单号 -->
        <el-table-column prop="order_id" label="订单号" width="140" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click="handleView(row)">
              {{ row.order_id }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 企业信息 -->
        <el-table-column label="企业信息" width="200">
          <template #default="{ row }">
            <div v-if="row.enterprise">
              <div class="enterprise-name">{{ row.enterprise.name }}</div>
              <div class="enterprise-id">ID: {{ row.enterprise.enterprise_id || 'N/A' }}</div>
            </div>
            <span v-else class="text-muted">未关联企业</span>
          </template>
        </el-table-column>

        <!-- 资产信息 -->
        <el-table-column label="资产信息" width="120">
          <template #default="{ row }">
            <span v-if="row.asset">{{ row.asset.asset_id }}</span>
            <span v-else class="text-muted">未关联资产</span>
          </template>
        </el-table-column>

        <!-- 用户信息 -->
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div v-if="row.user">
              <div>{{ row.user.name }}</div>
              <div class="text-muted">{{ row.user.mobile }}</div>
            </div>
            <span v-else class="text-muted">未关联用户</span>
          </template>
        </el-table-column>

        <!-- 创建方式 -->
        <el-table-column prop="creation_method" label="创建方式" width="100" />

        <!-- 订单类型 -->
        <el-table-column prop="order_type" label="订单类型" width="100" />

        <!-- 产品信息 -->
        <el-table-column label="产品信息" width="200">
          <template #default="{ row }">
            <div v-if="row.productItems && row.productItems.length > 0">
              <div v-for="item in row.productItems" :key="item.id">
                <div class="product-name">{{ item.product?.product_name || 'N/A' }}</div>
                <div class="product-details">
                  {{ item.user_count }}人 / {{ item.account_count }}套 / {{ item.duration_months }}月
                </div>
              </div>
            </div>
            <span v-else class="text-muted">无产品信息</span>
          </template>
        </el-table-column>

        <!-- 订单金额 -->
        <el-table-column label="订单金额" width="140">
          <template #default="{ row }">
            <div>标准: ¥{{ row.standard_amount }}</div>
            <div class="actual-amount">实付: ¥{{ row.actual_amount }}</div>
            <div v-if="row.tax_amount" class="tax-amount">税额: ¥{{ row.tax_amount }}</div>
          </template>
        </el-table-column>

        <!-- 支付信息 -->
        <el-table-column label="支付信息" width="120">
          <template #default="{ row }">
            <div>
              <el-tag :type="row.payment_status === '已支付' ? 'success' : 'warning'">
                {{ row.payment_status }}
              </el-tag>
            </div>
            <div v-if="row.payment_method" class="payment-method">
              {{ row.payment_method }}
            </div>
            <div v-if="row.payment_time" class="payment-time">
              {{ formatDateTime(row.payment_time) }}
            </div>
          </template>
        </el-table-column>

        <!-- 发票类型 -->
        <el-table-column prop="invoice_type" label="发票类型" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.invoice_type" size="small">
              {{ row.invoice_type }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 合伙人信息 -->
        <el-table-column label="合伙人信息" width="150">
          <template #default="{ row }">
            <div v-if="row.is_partner_order">
              <div>ID: {{ row.partner_user_id }}</div>
              <div class="commission-info">
                基础: {{ ((row.commission_base || 0) * 100).toFixed(2) }}%
              </div>
              <div class="commission-info">
                额外: {{ ((row.commission_extra || 0) * 100).toFixed(2) }}%
              </div>
              <div class="commission-amount">
                金额: ¥{{ row.commission_amount }}
              </div>
              <el-tag
                :type="row.commission_status === '已发放' ? 'success' : 'warning'"
                size="small"
              >
                {{ row.commission_status }}
              </el-tag>
            </div>
            <span v-else class="text-muted">否</span>
          </template>
        </el-table-column>

        <!-- 制单人 -->
        <el-table-column label="制单人" width="100">
          <template #default="{ row }">
            {{ row.creator?.name || 'N/A' }}
          </template>
        </el-table-column>

        <!-- 制单时间 -->
        <el-table-column label="制单时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.audit_status === '已审核'"
              type="warning"
              size="small"
              @click="handleRevertAudit(row)"
              :loading="auditingOrderId === row.id"
            >
              弃审
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { getProductOrders } from '@/api/order';
import { formatDateTime } from '@/utils/format';

// Composables
import { useOrderAudit } from '../composables/useOrderAudit';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const orders = ref([]);
const auditingOrderId = ref(null);

// 使用审核 composable
const { revertAudit } = useOrderAudit();

// 筛选表单
const filterForm = ref({
  orderType: '',
  creationMethod: '',
  paymentStatus: '',
  commissionStatus: '',
  orderId: ''
});

// 计算属性：过滤后的订单列表
const filteredOrders = computed(() => {
  let filtered = orders.value;
  
  if (filterForm.value.orderType) {
    filtered = filtered.filter(order => order.order_type === filterForm.value.orderType);
  }
  
  if (filterForm.value.creationMethod) {
    filtered = filtered.filter(order => order.creation_method === filterForm.value.creationMethod);
  }
  
  if (filterForm.value.paymentStatus) {
    filtered = filtered.filter(order => order.payment_status === filterForm.value.paymentStatus);
  }
  
  if (filterForm.value.commissionStatus) {
    filtered = filtered.filter(order => order.commission_status === filterForm.value.commissionStatus);
  }
  
  if (filterForm.value.orderId) {
    filtered = filtered.filter(order => 
      order.order_id.toLowerCase().includes(filterForm.value.orderId.toLowerCase())
    );
  }
  
  return filtered;
});

// 计算属性：统计数据
const stats = computed(() => {
  const filtered = filteredOrders.value;
  return {
    totalOrders: filtered.length,
    totalAmount: filtered.reduce((sum, order) => sum + parseFloat(order.actual_amount || 0), 0).toFixed(2),
    paidOrders: filtered.filter(order => order.payment_status === '已支付').length,
    commissionAmount: filtered.reduce((sum, order) => sum + parseFloat(order.commission_amount || 0), 0).toFixed(2)
  };
});

// 获取产品订单列表
const fetchOrders = async () => {
  loading.value = true;
  try {
    const response = await getProductOrders();
    orders.value = response || [];
  } catch (error) {
    console.error('获取产品订单列表失败:', error);
    ElMessage.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 表格合计行
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    
    if (column.property === 'actual_amount') {
      const values = data.map(item => Number(item.actual_amount));
      if (!values.every(value => isNaN(value))) {
        sums[index] = `¥${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0).toFixed(2)}`;
      } else {
        sums[index] = '';
      }
    } else if (column.property === 'commission_amount') {
      const values = data.map(item => Number(item.commission_amount || 0));
      sums[index] = `¥${values.reduce((prev, curr) => prev + curr, 0).toFixed(2)}`;
    } else {
      sums[index] = '';
    }
  });
  
  return sums;
};

// 事件处理函数
const handleCreate = () => {
  // 强制跳转到新增页面，避免重复路由问题
  router.push({ name: 'ProductOrderCreate', query: { t: Date.now() } });
};

const handleRefresh = () => {
  fetchOrders();
};

const handleFilter = () => {
  // 筛选逻辑已通过计算属性实现
};

const handleResetFilter = () => {
  filterForm.value = {
    orderType: '',
    creationMethod: '',
    paymentStatus: '',
    commissionStatus: '',
    orderId: ''
  };
};

const handleView = (order) => {
  router.push({
    name: 'ProductOrderForm',
    params: { id: order.id },
    query: { mode: 'view' }
  });
};

// 弃审订单
const handleRevertAudit = async (order) => {
  auditingOrderId.value = order.id;
  try {
    await revertAudit(order.id, async () => {
      // 弃审成功后刷新列表
      await fetchOrders();
    });
  } finally {
    auditingOrderId.value = null;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders();
});
</script>

<style scoped>
.product-order-list {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 筛选栏 */
.filter-bar {
  margin-bottom: 20px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  padding: 20px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.enterprise-name {
  font-weight: 500;
  color: #303133;
}

.enterprise-id {
  font-size: 12px;
  color: #909399;
}

.product-name {
  font-weight: 500;
  color: #303133;
}

.product-details {
  font-size: 12px;
  color: #909399;
}

.actual-amount {
  font-weight: 500;
  color: #67c23a;
}

.tax-amount {
  font-size: 12px;
  color: #909399;
}

.payment-method, .payment-time {
  font-size: 12px;
  color: #909399;
}

.commission-info, .commission-amount {
  font-size: 12px;
  color: #909399;
}

.text-muted {
  color: #909399;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filter-bar .el-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .filter-bar .el-form-item {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .product-order-list {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .page-header h2 {
    font-size: 20px;
  }

  .filter-bar {
    padding: 16px;
  }
}
</style>
