/**
 * 全局响应拦截器
 * 统一处理API响应格式，确保数据结构一致性
 */

/**
 * 响应拦截器配置
 */
const ResponseInterceptor = {
  /**
   * 处理API响应数据
   * @param {*} response - 原始响应数据
   * @param {Object} config - 配置选项
   * @returns {*} 处理后的数据
   */
  process(response, config = {}) {
    const { 
      expectArray = false,  // 是否期望数组类型
      expectObject = false, // 是否期望对象类型
      defaultValue = null,  // 默认值
      logErrors = true      // 是否记录错误
    } = config;

    try {
      // 如果响应为空或undefined
      if (response === null || response === undefined) {
        if (logErrors) {
          console.warn('API响应为空');
        }
        return this.getDefaultValue(expectArray, expectObject, defaultValue);
      }

      // 如果期望数组类型
      if (expectArray) {
        return this.processArrayResponse(response, logErrors);
      }

      // 如果期望对象类型
      if (expectObject) {
        return this.processObjectResponse(response, logErrors);
      }

      // 默认返回原始响应
      return response;
    } catch (error) {
      if (logErrors) {
        console.error('响应拦截器处理失败:', error);
      }
      return this.getDefaultValue(expectArray, expectObject, defaultValue);
    }
  },

  /**
   * 处理数组类型响应
   * @param {*} response - 响应数据
   * @param {boolean} logErrors - 是否记录错误
   * @returns {Array} 数组数据
   */
  processArrayResponse(response, logErrors = true) {
    // 直接是数组
    if (Array.isArray(response)) {
      return response;
    }

    // 分页格式: {records: [], total: x, page: x, pageSize: x}
    if (response && response.records && Array.isArray(response.records)) {
      return response.records;
    }

    // 标准格式: {data: []}
    if (response && response.data && Array.isArray(response.data)) {
      return response.data;
    }

    // 成功格式: {success: true, data: []}
    if (response && response.success && response.data && Array.isArray(response.data)) {
      return response.data;
    }

    // 嵌套数据格式: {data: {records: []}}
    if (response && response.data && response.data.records && Array.isArray(response.data.records)) {
      return response.data.records;
    }

    // 如果都不匹配，记录警告并返回空数组
    if (logErrors) {
      console.warn('API响应格式不符合预期，期望数组类型:', response);
      console.warn('支持的格式: [], {records: []}, {data: []}, {success: true, data: []}');
    }
    return [];
  },

  /**
   * 处理对象类型响应
   * @param {*} response - 响应数据
   * @param {boolean} logErrors - 是否记录错误
   * @returns {Object} 对象数据
   */
  processObjectResponse(response, logErrors = true) {
    // 标准格式: {data: {}}
    if (response && response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
      return response.data;
    }

    // 成功格式: {success: true, data: {}}
    if (response && response.success && response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
      return response.data;
    }

    // 直接是对象
    if (response && typeof response === 'object' && !Array.isArray(response) && !response.data && !response.records) {
      return response;
    }

    // 如果都不匹配，记录警告并返回空对象
    if (logErrors) {
      console.warn('API响应格式不符合预期，期望对象类型:', response);
      console.warn('支持的格式: {}, {data: {}}, {success: true, data: {}}');
    }
    return {};
  },

  /**
   * 获取默认值
   * @param {boolean} expectArray - 是否期望数组
   * @param {boolean} expectObject - 是否期望对象
   * @param {*} defaultValue - 自定义默认值
   * @returns {*} 默认值
   */
  getDefaultValue(expectArray, expectObject, defaultValue) {
    if (defaultValue !== null) {
      return defaultValue;
    }
    if (expectArray) {
      return [];
    }
    if (expectObject) {
      return {};
    }
    return null;
  },

  /**
   * 创建拦截器实例
   * @param {Object} defaultConfig - 默认配置
   * @returns {Function} 拦截器函数
   */
  create(defaultConfig = {}) {
    return (response, config = {}) => {
      const mergedConfig = { ...defaultConfig, ...config };
      return this.process(response, mergedConfig);
    };
  }
};

// 预定义的拦截器实例
const arrayInterceptor = ResponseInterceptor.create({ expectArray: true });
const objectInterceptor = ResponseInterceptor.create({ expectObject: true });

module.exports = {
  ResponseInterceptor,
  arrayInterceptor,
  objectInterceptor
};
