/* pages/order-detail/order-detail.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 80rpx;
}

/* 内容区域 */
.content-container {
  padding-top: calc(var(--status-bar-height, 44rpx) + 88rpx + 24rpx);
  padding-left: 24rpx;
  padding-right: 24rpx;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  font-size: 28rpx;
  opacity: 0.8;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.retry-btn {
  margin-top: 32rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

/* 订单概览卡片 */
.overview-card {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.order-main {
  flex: 1;
}

.order-id {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.order-category {
  font-size: 24rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.order-amount {
  font-size: 36rpx;
  font-weight: 600;
  text-align: right;
}

.order-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-status-tag {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
}

.payment-status-tag.paid {
  background: rgba(82, 196, 26, 0.9);
}

.payment-status-tag.pending {
  background: rgba(250, 173, 20, 0.9);
}

.order-date {
  font-size: 22rpx;
  opacity: 0.8;
}

/* 简约信息列表 */
.simple-info-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.simple-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.simple-info-item:last-child {
  border-bottom: none;
}

.simple-info-item .info-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}

.simple-info-item .info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.order-type-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx !important;
  font-weight: 500;
}

/* 产品列表简化样式 */
.product-item {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.product-version-tag {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  margin-left: 12rpx;
}

.product-specs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.spec-text {
  background: #e6f7ff;
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.product-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-main {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.price-main .price-label {
  font-size: 24rpx;
  color: #666;
}

.price-main .price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

.price-details {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.discount-tag {
  background: #ff4d4f;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.feature-tag {
  background: #f6ffed;
  color: #52c41a;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  border: 1rpx solid #b7eb8f;
}

/* 服务列表简化样式 */
.service-item {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.service-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.service-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

.service-remark .remark-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 价格汇总简化 */
.price-summary {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.summary-item.total {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
  margin-top: 8rpx;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
}

.summary-item.total .summary-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.summary-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.summary-item.total .summary-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

.summary-value.commission {
  color: #52c41a;
}

/* 关联资产简化 */
.asset-info {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.asset-info:active {
  background: #f0f0f0;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.asset-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.asset-status-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.asset-status-tag.online {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.asset-status-tag.offline {
  background: #fff2e8;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}

.asset-id {
  font-size: 22rpx;
  color: #999;
}

/* 备注信息简化 */
.remark-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #fafafa;
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
}

/* 操作按钮简化 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 40rpx 32rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  position: sticky;
  bottom: 0;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
}

.action-btn.primary:active {
  background: #096dd9;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

.action-btn.secondary:active {
  background: #d9d9d9;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.order-type {
  display: flex;
  align-items: center;
}

.type-icon {
  font-size: 40rpx;
  margin-right: 12rpx;
}

.type-text {
  font-size: 32rpx;
  font-weight: 600;
}

.order-amount {
  text-align: right;
}

.amount-label {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 8rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: 700;
}

.status-row {
  display: flex;
  gap: 32rpx;
}

.status-item {
  flex: 1;
  text-align: center;
}

.status-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.status-badge.pending {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
}

.status-badge.paid {
  background: rgba(40, 167, 69, 0.9);
}

.status-badge.approved {
  background: rgba(40, 167, 69, 0.9);
}

.status-badge.rejected {
  background: rgba(220, 53, 69, 0.9);
}

/* 状态卡片 */
.status-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.status-header {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.status-icon.pending {
  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
}

.status-icon.paid {
  background: linear-gradient(135deg, #55efc4, #00b894);
}

.status-icon.completed {
  background: linear-gradient(135deg, #a29bfe, #6c5ce7);
}

.status-icon.cancelled {
  background: linear-gradient(135deg, #fd79a8, #e84393);
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-text.pending {
  color: #e17055;
}

.status-text.paid {
  color: #00b894;
}

.status-text.completed {
  color: #6c5ce7;
}

.status-text.cancelled {
  color: #e84393;
}

.status-desc {
  font-size: 24rpx;
  color: #666;
}

/* 订单信息列表 */
.order-info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  word-break: break-all;
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.product-item {
  display: flex;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.product-info {
  flex: 1;
  margin-right: 24rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.product-spec, .product-quantity {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.product-price {
  text-align: right;
}

.unit-price {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.total-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #e17055;
}

/* 价格汇总 */
.price-summary {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.summary-item.total {
  border-top: 2rpx solid #e0e0e0;
  padding-top: 24rpx;
  margin-top: 16rpx;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-item.total .summary-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #e17055;
}

.summary-item.total .summary-value {
  font-size: 36rpx;
  color: #e17055;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.action-btn.secondary:active {
  background: #e9ecef;
}

/* 新增样式 - 信息网格布局 */
.info-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-grid .info-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fc;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
  border-bottom: none;
}

.info-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 40rpx;
  text-align: center;
}

.info-content {
  flex: 1;
}

.info-content .info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
  width: auto;
}

.info-content .info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: left;
}

/* 产品规格样式 */
.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.product-version {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.product-specs {
  display: flex;
  gap: 24rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: white;
  border-radius: 20rpx;
  border: 1rpx solid #e9ecef;
}

.spec-icon {
  font-size: 24rpx;
}

.spec-label {
  font-size: 24rpx;
  color: #666;
}

.spec-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 产品定价样式 */
.product-pricing {
  background: white;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.price-row.total {
  border-top: 1rpx solid #e9ecef;
  margin-top: 8rpx;
  padding-top: 12rpx;
  font-weight: 600;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.price-value {
  font-size: 26rpx;
  color: #333;
}

.price-value.discount {
  color: #e74c3c;
}

.price-row.total .price-value {
  color: #667eea;
  font-size: 30rpx;
  font-weight: 600;
}

/* 产品功能样式 */
.product-features {
  margin-bottom: 16rpx;
}

.features-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.feature-tag {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.2);
}

/* 产品活动样式 */
.product-activity {
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 8rpx;
  padding: 12rpx;
}

.activity-label {
  font-size: 24rpx;
  color: #856404;
  font-weight: 500;
}

.activity-text {
  font-size: 24rpx;
  color: #856404;
}

/* 服务订单样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  background: #f8f9fc;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid #28a745;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.service-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #28a745;
}

.service-details {
  margin-bottom: 12rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6rpx 0;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
}

.service-remark {
  background: white;
  border-radius: 8rpx;
  padding: 12rpx;
  border: 1rpx solid #e9ecef;
}

.remark-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.remark-text {
  font-size: 24rpx;
  color: #333;
}

/* 费用汇总样式更新 */
.summary-divider {
  height: 1rpx;
  background: #e9ecef;
  margin: 12rpx 0;
}

.summary-item.total {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8rpx;
  padding: 12rpx;
  margin-top: 8rpx;
}

.summary-value.commission {
  color: #fd7e14;
}

/* 支付信息样式 */
.payment-info {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #e9ecef;
}

.payment-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.payment-icon {
  font-size: 28rpx;
}

.payment-label {
  font-size: 26rpx;
  color: #666;
}

.payment-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 关联资产样式 */
.asset-info {
  padding: 20rpx;
  background: #f8f9fc;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
}

.asset-info:active {
  background: #e9ecf3;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.asset-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.asset-id {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.asset-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-dot.online {
  background: #28a745;
}

.status-dot.offline {
  background: #dc3545;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

/* 分润信息样式 */
.commission-info {
  display: flex;
  gap: 32rpx;
}

.commission-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  background: #f8f9fc;
  border-radius: 12rpx;
}

.commission-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.commission-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #fd7e14;
}

.commission-status {
  font-size: 26rpx;
  font-weight: 500;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.commission-status.paid {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.commission-status.pending {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

/* 备注信息样式 */
.remark-section {
  margin-bottom: 20rpx;
}

.remark-section:last-child {
  margin-bottom: 0;
}

.remark-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.remark-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fc;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
}

/* 按钮图标样式 */
.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 新增订单详情页面样式 */

/* 订单概览卡片新样式 */
.order-title-line {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.order-enterprise {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

.basic-info-section {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 产品明细新样式 */
.product-item-new {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.product-header-new {
  margin-bottom: 20rpx;
}

.product-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.product-version-tag {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

.product-details-grid {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

.price-highlight {
  color: #1890ff;
  font-weight: 600;
}

.discount-value {
  color: #ff6b35;
  font-weight: 600;
}

.product-specs-new {
  display: flex;
  gap: 8rpx;
}

.spec-tag {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}

.product-features-new {
  padding-top: 16rpx;
  border-top: 1rpx solid #f8f9fa;
}

/* 详细功能显示样式 */
.product-features-detailed {
  padding-top: 16rpx;
  border-top: 1rpx solid #f8f9fa;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.feature-item-detailed {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
}

.feature-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.feature-price {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 简单功能显示样式 */
.product-features-simple {
  padding-top: 16rpx;
  border-top: 1rpx solid #f8f9fa;
}

.features-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.features-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.feature-tag-new, .feature-tag-simple {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

/* 费用明细新样式 */
.price-summary-new {
  background: #f8f9fc;
  border-radius: 12rpx;
  padding: 20rpx;
}

.summary-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.summary-row:last-child {
  border-bottom: none;
  font-weight: 600;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-value {
  font-size: 28rpx;
  color: #333;
}

/* 关联资产新样式 */
.asset-info-new {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fc;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #e8f4fd;
}

/* 增强的资产信息样式 */
.asset-info-enhanced {
  background: #f8f9fc;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e8f4fd;
  cursor: pointer;
}

.asset-info-enhanced:active {
  background: #e8f4fd;
}

.asset-header-section {
  margin-bottom: 20rpx;
}

.asset-details-section {
  margin-bottom: 20rpx;
}

.asset-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.asset-detail-row:last-child {
  border-bottom: none;
}

.asset-detail-row .detail-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}

.asset-detail-row .detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.asset-main-info {
  flex: 1;
}

.asset-title-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.asset-id-text {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}

.asset-name-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.asset-company {
  font-size: 24rpx;
  color: #999;
}

.view-asset-btn {
  color: #1890ff;
  font-size: 26rpx;
  font-weight: 500;
}

/* 附件信息样式 */
.attachments-list {
  background: #f8f9fc;
  border-radius: 12rpx;
  padding: 16rpx;
}

/* 增强的附件列表样式 */
.attachments-list-enhanced {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.attachment-item-enhanced {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.attachment-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.file-icon {
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fc;
  border-radius: 8rpx;
}

.attachment-main-info {
  flex: 1;
}

.attachment-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.attachment-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.attachment-type-tag {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.attachment-type-tag.pdf {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.attachment-type-tag.doc {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.attachment-type-tag.excel {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.attachment-type-tag.image {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.attachment-type-tag.default {
  background: rgba(0, 0, 0, 0.06);
  color: #666;
}

.attachment-size {
  font-size: 20rpx;
  color: #999;
}

.attachment-actions-enhanced {
  display: flex;
  gap: 12rpx;
}

.attachment-btn-enhanced {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  background: none;
}

.attachment-btn-enhanced.view-btn {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.attachment-btn-enhanced.download-btn {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

.no-attachments {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
}

.no-attachments-text {
  font-size: 26rpx;
}

/* 原有样式保持兼容 */
.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #fff;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-info {
  flex: 1;
}

.attachment-type {
  font-size: 22rpx;
  color: #999;
}

.attachment-actions {
  display: flex;
  gap: 16rpx;
}

.attachment-btn {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.view-btn {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}

.download-btn {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .order-title-line {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .product-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .product-specs-new {
    flex-wrap: wrap;
  }

  .asset-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .attachment-actions {
    align-self: flex-end;
  }
}

/* 增强的产品功能样式 */
.product-features-enhanced {
  margin-top: 24rpx;
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item-enhanced {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.feature-status-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.feature-status-tag.purchased {
  background: #e6f7ff;
  color: #1890ff;
}

.feature-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.feature-price-label {
  font-size: 24rpx;
  color: #666;
}

.feature-price-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
}

.feature-description {
  padding: 8rpx 0;
}

.feature-desc-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.features-summary {
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  border: 1rpx solid #d6e4ff;
}

.features-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.features-summary .summary-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.features-summary .summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1890ff;
}

/* 资产ID复制样式 */
.asset-id-copy {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
}

.asset-id-copy:active {
  opacity: 0.7;
}

/* 附件上传人信息 */
.attachment-uploader {
  font-size: 20rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 紧凑型附件按钮 */
.attachment-btn-compact {
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: 1rpx solid #d9d9d9;
  background: white;
  color: #666;
  margin: 0;
  margin-left: 8rpx;
}

.attachment-btn-compact.view-btn {
  color: #1890ff;
  border-color: #1890ff;
}

.attachment-btn-compact.view-btn:active {
  background: #f0f8ff;
}

.attachment-btn-compact.download-btn {
  color: #52c41a;
  border-color: #52c41a;
}

.attachment-btn-compact.download-btn:active {
  background: #f6ffed;
}

/* 折扣和活动样式 */
.discount-amount {
  color: #ff4d4f;
}

.activity-desc {
  color: #1890ff;
  font-size: 24rpx;
}

/* 已购功能整体区域样式 */
.purchased-features-section {
  background: #f8f9fc;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 24rpx 0;
}

.purchased-features-section .detail-row {
  margin-bottom: 16rpx;
}

.purchased-features-section .detail-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 产品功能标签样式（扁平化，与资产详情页保持一致） */
.purchased-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag.purchased {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
  font-size: 24rpx;
}

.feature-tag .feature-name {
  font-weight: 500;
}

.feature-tag .feature-price {
  font-size: 22rpx;
  color: #ff6b35;
  font-weight: 600;
}

.no-features {
  color: #999;
  font-style: italic;
}
