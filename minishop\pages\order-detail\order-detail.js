// pages/order-detail/order-detail.js
const api = require('../../utils/api');

Page({
  data: {
    orderId: null,
    order: null,
    loading: true,
    error: null
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    } else {
      this.showError('缺少订单ID参数');
    }
  },

  onShow() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '订单详情'
    });
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail() {
    try {
      this.setData({ loading: true, error: null });

      const response = await api.get(`/orders/${this.data.orderId}`);
      const orderData = response.data || response;



      if (orderData && orderData.id) {
        // 处理产品功能数据
        if (orderData.productItems && orderData.productItems.length > 0) {
          orderData.productItems.forEach(item => {
            if (item.product && item.product.features && item.selected_features) {
              // 为每个产品功能添加选中状态
              item.product.features.forEach(feature => {
                feature.isSelected = item.selected_features.includes(feature.id);
              });

              // 创建已选择的功能列表
              item.selectedFeaturesList = item.product.features.filter(feature =>
                item.selected_features.includes(feature.id)
              );
            }
          });
        }

        this.setData({
          order: orderData,
          loading: false
        });

        // 更新页面标题
        if (orderData.order_id) {
          wx.setNavigationBarTitle({
            title: `订单 ${orderData.order_id}`
          });
        }
      } else {
        this.showError('订单数据格式错误');
      }
    } catch (error) {
      this.showError('加载订单详情失败');
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    this.setData({
      loading: false,
      error: message
    });
    wx.showToast({
      title: message,
      icon: 'none'
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 刷新页面
   */
  onPullDownRefresh() {
    this.loadOrderDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showToast({
      title: '请使用右下角咨询按钮',
      icon: 'none'
    });
  },

  /**
   * 支付订单
   */
  payOrder() {
    wx.showToast({
      title: '支付功能开发中',
      icon: 'none'
    });
  },

  /**
   * 取消订单
   */
  cancelOrder() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '取消订单功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 查看关联资产
   */
  viewAsset(e) {
    const assetId = e.currentTarget.dataset.assetId;
    if (assetId) {
      wx.navigateTo({
        url: `/pages/asset-detail/asset-detail?id=${assetId}`
      });
    } else {
      wx.showToast({
        title: '资产ID不存在',
        icon: 'none'
      });
    }
  },

  /**
   * 复制资产ID
   */
  copyAssetId(e) {
    e.stopPropagation(); // 阻止事件冒泡，避免触发查看资产
    const text = e.currentTarget.dataset.text;
    if (text) {
      wx.setClipboardData({
        data: text,
        success: () => {
          wx.showToast({
            title: '资产ID已复制',
            icon: 'success'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 查看附件
   */
  viewAttachment(e) {
    let url = e.currentTarget.dataset.url;
    const fileType = e.currentTarget.dataset.type || '';

    if (!url) {
      wx.showToast({
        title: '附件链接不存在',
        icon: 'none'
      });
      return;
    }

    // 如果不是完整URL，添加服务器地址
    if (!url.startsWith('http')) {
      const app = getApp();
      const baseUrl = app.globalData.baseUrl || 'http://localhost:3000';
      url = baseUrl + (url.startsWith('/') ? url : '/' + url);
    }

    console.log('查看附件:', { url, fileType });

    // 根据文件类型处理
    const lowerType = fileType.toLowerCase();

    // 图片文件预览
    if (lowerType.includes('image') || url.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) {
      wx.previewImage({
        urls: [url],
        current: url,
        fail: (err) => {
          console.error('图片预览失败:', err);
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
      return;
    }

    // PDF文件处理
    if (lowerType.includes('pdf') || url.match(/\.pdf$/i)) {
      // 尝试使用微信内置PDF查看器
      wx.openDocument({
        filePath: url,
        fileType: 'pdf',
        success: () => {
          console.log('PDF打开成功');
        },
        fail: (err) => {
          console.error('PDF打开失败:', err);
          // 如果内置查看器失败，尝试下载
          this.downloadAttachment(e);
        }
      });
      return;
    }

    // 其他文件类型，提示用户选择操作
    wx.showActionSheet({
      itemList: ['下载文件', '复制链接'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 下载文件
          this.downloadAttachment(e);
        } else if (res.tapIndex === 1) {
          // 复制链接
          wx.setClipboardData({
            data: url,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 下载附件
   */
  downloadAttachment(e) {
    let url = e.currentTarget.dataset.url;
    const fileName = e.currentTarget.dataset.name;

    if (!url) {
      wx.showToast({
        title: '附件链接不存在',
        icon: 'none'
      });
      return;
    }

    // 如果不是完整URL，添加服务器地址
    if (!url.startsWith('http')) {
      const app = getApp();
      const baseUrl = app.globalData.baseUrl || 'http://localhost:3000';
      url = baseUrl + (url.startsWith('/') ? url : '/' + url);
    }

    wx.showLoading({
      title: '下载中...'
    });

    wx.downloadFile({
      url: url,
      success: (res) => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            success: () => {
              console.log('打开文档成功');
            },
            fail: (err) => {
              console.error('打开文档失败:', err);
              wx.showToast({
                title: '无法打开该文件',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('下载失败:', err);
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 重试加载
   */
  retryLoad() {
    this.loadOrderDetail();
  }
});
