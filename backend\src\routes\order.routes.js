const express = require('express');
const router = express.Router();
const orderController = require('../controllers/order.controller.js');
const upload = require('../middleware/upload');
const { employee } = require('../middleware/auth');
const { verifyToken, requireUserType } = require('../middleware/auth/base');

// --- 订单ID生成路由 ---
// 获取下一个可用的订单ID（支持产品订单PO和服务订单SO）
router.get('/next-id', employee.verifyEmployee, orderController.getNextOrderId);

// 检查订单号是否已存在
router.get('/check-id/:orderId', employee.verifyEmployee, orderController.checkOrderIdExists);

// --- 用户订单相关路由 ---
// 获取用户订单列表（用于小程序）
router.get('/', [verifyToken], orderController.getUserOrders);

// 获取合伙人订单列表（用于合伙人小程序）
router.get('/partner', [verifyToken], orderController.getPartnerOrders);

// --- 订单审核相关路由 ---
// 获取订单审核列表（所有待审核的订单）
router.get('/review', employee.verifyEmployee, orderController.getOrdersForReview);

// 审核订单 - 通过审核
router.post('/:id/approve', employee.verifyEmployee, orderController.approveOrder);

// 审核订单 - 拒绝审核
router.post('/:id/reject', employee.verifyEmployee, orderController.rejectOrder);

// --- 产品订单相关路由 ---
// 获取产品订单列表（已审核通过的产品订单）
router.get('/product', employee.verifyEmployee, orderController.getProductOrders);

// 创建产品订单
router.post('/product', employee.verifyEmployee, orderController.createProductOrder);

// --- 服务订单相关路由 ---
// 获取服务订单列表（已审核通过的服务订单）
router.get('/service', employee.verifyEmployee, orderController.getServiceOrders);

// 创建服务订单
router.post('/service', employee.verifyEmployee, orderController.createServiceOrder);

// --- 通用订单操作路由 ---
// 获取单个订单详情（员工和用户都可以访问）
router.get('/:id', [verifyToken, requireUserType(['employee', 'user'])], orderController.getOrderById);

// 更新订单
router.put('/:id', employee.verifyEmployee, orderController.updateOrder);

// 删除订单
router.delete('/:id', employee.verifyEmployee, orderController.deleteOrder);

// --- 订单附件路由 ---
// 处理附件上传的预检请求
router.options('/:orderId/attachments', (req, res) => {
    res.header('Access-Control-Allow-Origin', 'https://admin.bogoo.net');
    res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Accept,Origin');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.sendStatus(200);
});

// 上传订单附件 - 修复字段名匹配问题
router.post('/:orderId/attachments', [employee.verifyEmployee, upload.single('file')], orderController.addAttachment);

// 获取指定订单的所有附件
router.get('/:orderId/attachments', employee.verifyEmployee, orderController.getAttachments);

// 删除指定ID的附件
router.delete('/:orderId/attachments/:attachmentId', employee.verifyEmployee, orderController.deleteAttachment);

// 下载指定ID的附件
router.get('/:orderId/attachments/:attachmentId/download', employee.verifyEmployee, orderController.downloadAttachment);

// 预览指定ID的附件
router.get('/:orderId/attachments/:attachmentId/preview', employee.verifyEmployee, orderController.previewAttachment);

module.exports = router;