/* pages/asset-detail/asset-detail.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left, .navbar-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 36rpx;
}

.nav-icon {
  font-size: 32rpx;
  font-weight: 600;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: white;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 内容容器 */
.content-container {
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx) + 32rpx);
  padding-left: 32rpx;
  padding-right: 32rpx;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
}

.info-card.overview-card {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx;
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.product-info-main {
  flex: 1;
}

.product-name {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.asset-id {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* 资产概览样式 */
.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.asset-main {
  flex: 1;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color: white;
}

.asset-id {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.asset-status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
}

.asset-status-tag.online {
  background: rgba(82, 196, 26, 0.9);
}

.asset-status-tag.expired {
  background: rgba(255, 77, 79, 0.9);
}

.asset-specs {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.asset-specs .spec-text {
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

/* 基本信息集成样式 */
.basic-info-section {
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  padding-top: 20rpx;
}

.basic-info-section .info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.basic-info-section .info-row:last-child {
  margin-bottom: 0;
}

.basic-info-section .info-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.basic-info-section .info-value {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

.basic-info-section .version-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

/* 卡片标题 */
.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 功能网格样式 */
.features-grid {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.features-grid .feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 3rpx solid #1890ff;
}

.features-grid .feature-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.features-grid .feature-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.features-grid .feature-status.selected {
  background: #f6ffed;
  color: #52c41a;
}

.features-grid .feature-status.available {
  background: #f0f0f0;
  color: #999;
}

/* 到期信息网格 */
.expiry-grid {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.expiry-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 3rpx solid #1890ff;
}

.expiry-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.expiry-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.expiry-status.normal {
  background: #f6ffed;
  color: #52c41a;
}

.expiry-status.warning {
  background: #fff7e6;
  color: #fa8c16;
}

.expiry-status.expired {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 价格网格样式 */
.price-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.price-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 3rpx solid #1890ff;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.price-value {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 600;
}

.price-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #e6f7ff;
  border-radius: 8rpx;
  border-left: 3rpx solid #1890ff;
}

.total-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.total-value {
  font-size: 32rpx;
  color: #1890ff;
  font-weight: 600;
}

/* 搜索按钮样式 */
.search-btn {
  background: #1890ff;
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.search-btn:active {
  background: #096dd9;
}

/* 关联订单样式 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.order-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx;
  border-left: 3rpx solid #1890ff;
}

.order-item:active {
  background: #e9ecef;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.order-id {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.order-amount {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
}

.order-info {
  display: flex;
  gap: 12rpx;
  align-items: center;
  font-size: 22rpx;
}

.order-type {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.order-status {
  background: #f6ffed;
  color: #52c41a;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.order-date {
  color: #999;
}

/* 无订单提示 */
.no-orders {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
}

.no-orders-text {
  display: block;
  font-size: 26rpx;
  margin-bottom: 8rpx;
}

.search-tip {
  display: block;
  font-size: 22rpx;
  color: #ccc;
}

/* 备注信息样式 */
.remark-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #1890ff;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  position: sticky;
  bottom: 0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
}

.action-btn.primary:active {
  background: #096dd9;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
  border: 1rpx solid #d9d9d9;
}

.action-btn.secondary:active {
  background: #d9d9d9;
}

/* 删除无关样式，保持简洁 */

/* 删除重复的样式定义 */

/* 删除复杂的装饰性样式 */

/* 删除旧的功能列表样式，已在上面重新定义 */

/* 删除旧的到期信息样式，已在上面重新定义 */

/* 删除旧的价格信息样式，已在上面重新定义 */

/* 删除激活信息样式，已移除该功能 */

/* 删除复杂的备注样式，已在上面重新定义 */

/* 删除复杂的操作按钮样式，已在上面重新定义 */

/* 删除无关的复杂样式 */

/* 删除无关的样式，保持简洁 */

/* 删除重复的功能列表样式 */

/* 删除重复的到期信息样式 */

/* 删除重复的价格网格样式 */

/* 删除激活信息样式，已移除该功能 */

/* 资产概览卡片新增样式 */
.asset-title-line {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.asset-id-copy {
  background: rgba(255, 255, 255, 0.9);
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.asset-version {
  background: rgba(255, 255, 255, 0.9);
  color: #52c41a;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  border: 1rpx solid rgba(82, 196, 26, 0.3);
}

.asset-company {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 产品功能标签样式 */
.purchased-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag.purchased {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
  font-size: 24rpx;
}

.feature-tag .feature-name {
  font-weight: 500;
}

.feature-tag .feature-price {
  font-size: 22rpx;
  color: #ff6b35;
  font-weight: 600;
}

/* 到期信息优化样式 */
.expiry-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f8f9fc;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.expiry-content {
  flex: 1;
}

.expiry-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.expiry-date {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.expiry-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.expiry-status.normal {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.expiry-status.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #fa8c16;
}

.expiry-status.expired {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

/* 关联订单模块样式 */
.toggle-btn {
  background: #1890ff;
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.orders-collapsed {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.collapsed-text {
  font-size: 26rpx;
}

.search-section {
  margin-bottom: 16rpx;
  text-align: center;
}

.search-btn {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}
