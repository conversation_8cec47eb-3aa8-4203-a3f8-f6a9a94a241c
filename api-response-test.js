/**
 * API响应处理测试
 * 验证 normalizeApiResponse 函数的正确性
 */

// 模拟 normalizeApiResponse 函数
function normalizeApiResponse(response, type = 'array') {
  if (!response) {
    return type === 'array' ? [] : {};
  }

  if (type === 'array') {
    // 处理数组类型的响应
    if (Array.isArray(response)) {
      return response;
    } else if (response.records && Array.isArray(response.records)) {
      // 分页格式: {records: [], total: x, page: x, pageSize: x}
      return response.records;
    } else if (response.data && Array.isArray(response.data)) {
      // 标准格式: {data: []}
      return response.data;
    } else if (response.success && response.data && Array.isArray(response.data)) {
      // 成功格式: {success: true, data: []}
      return response.data;
    } else {
      console.warn('API响应格式不符合预期，期望数组类型:', response);
      return [];
    }
  } else {
    // 处理对象类型的响应
    if (response.data && typeof response.data === 'object') {
      return response.data;
    } else if (response.success && response.data && typeof response.data === 'object') {
      return response.data;
    } else if (typeof response === 'object' && !Array.isArray(response)) {
      return response;
    } else {
      console.warn('API响应格式不符合预期，期望对象类型:', response);
      return {};
    }
  }
}

// 测试用例
function runTests() {
  console.log('开始API响应处理测试...\n');

  // 测试1: 直接数组响应
  console.log('测试1: 直接数组响应');
  const directArray = [{ id: 1, name: 'test' }];
  const result1 = normalizeApiResponse(directArray, 'array');
  console.log('输入:', directArray);
  console.log('输出:', result1);
  console.log('是否为数组:', Array.isArray(result1));
  console.log('通过:', Array.isArray(result1) && result1.length === 1);
  console.log('');

  // 测试2: 分页格式响应（实际错误场景）
  console.log('测试2: 分页格式响应（修复前会出错的场景）');
  const paginatedResponse = {
    records: [{ id: 1, name: 'asset1' }],
    total: 1,
    page: 1,
    pageSize: 20
  };
  const result2 = normalizeApiResponse(paginatedResponse, 'array');
  console.log('输入:', paginatedResponse);
  console.log('输出:', result2);
  console.log('是否为数组:', Array.isArray(result2));
  console.log('通过:', Array.isArray(result2) && result2.length === 1);
  console.log('');

  // 测试3: 成功格式响应（另一个错误场景）
  console.log('测试3: 成功格式响应（修复前会出错的场景）');
  const successResponse = {
    success: true,
    data: [{ id: 1, name: 'order1' }]
  };
  const result3 = normalizeApiResponse(successResponse, 'array');
  console.log('输入:', successResponse);
  console.log('输出:', result3);
  console.log('是否为数组:', Array.isArray(result3));
  console.log('通过:', Array.isArray(result3) && result3.length === 1);
  console.log('');

  // 测试4: 标准格式响应
  console.log('测试4: 标准格式响应');
  const standardResponse = {
    data: [{ id: 1, name: 'item1' }]
  };
  const result4 = normalizeApiResponse(standardResponse, 'array');
  console.log('输入:', standardResponse);
  console.log('输出:', result4);
  console.log('是否为数组:', Array.isArray(result4));
  console.log('通过:', Array.isArray(result4) && result4.length === 1);
  console.log('');

  // 测试5: 无效响应（应该返回空数组）
  console.log('测试5: 无效响应（应该返回空数组）');
  const invalidResponse = { message: 'error' };
  const result5 = normalizeApiResponse(invalidResponse, 'array');
  console.log('输入:', invalidResponse);
  console.log('输出:', result5);
  console.log('是否为数组:', Array.isArray(result5));
  console.log('通过:', Array.isArray(result5) && result5.length === 0);
  console.log('');

  // 测试6: null/undefined响应
  console.log('测试6: null/undefined响应');
  const result6 = normalizeApiResponse(null, 'array');
  const result7 = normalizeApiResponse(undefined, 'array');
  console.log('null输入输出:', result6);
  console.log('undefined输入输出:', result7);
  console.log('通过:', Array.isArray(result6) && Array.isArray(result7));
  console.log('');

  console.log('测试完成！');
}

// 模拟实际使用场景
function simulateActualUsage() {
  console.log('\n模拟实际使用场景...\n');

  // 模拟资产API响应（修复前会出错）
  const assetApiResponse = {
    records: [
      { id: 1, name: 'Asset 1', product_expiry_date: '2024-12-31' },
      { id: 2, name: 'Asset 2', product_expiry_date: '2025-06-30' }
    ],
    total: 2,
    page: 1,
    pageSize: 20
  };

  console.log('资产API响应:', assetApiResponse);
  
  // 使用修复后的处理方式
  const assets = normalizeApiResponse(assetApiResponse, 'array');
  console.log('处理后的资产数组:', assets);
  
  // 现在可以安全地使用数组方法
  try {
    const processedAssets = assets.map(asset => ({
      ...asset,
      displayName: `资产: ${asset.name}`
    }));
    console.log('处理成功，结果:', processedAssets);
  } catch (error) {
    console.error('处理失败:', error.message);
  }

  console.log('');

  // 模拟订单API响应（修复前会出错）
  const orderApiResponse = {
    success: true,
    data: [
      { id: 1, order_number: 'ORD001', payment_status: '待付款' },
      { id: 2, order_number: 'ORD002', payment_status: '已付款' }
    ]
  };

  console.log('订单API响应:', orderApiResponse);
  
  // 使用修复后的处理方式
  const orders = normalizeApiResponse(orderApiResponse, 'array');
  console.log('处理后的订单数组:', orders);
  
  // 现在可以安全地使用数组方法
  try {
    const pendingOrders = orders.filter(order => 
      order.payment_status === '待付款' || order.payment_status === 'pending'
    );
    console.log('待付款订单:', pendingOrders);
  } catch (error) {
    console.error('处理失败:', error.message);
  }
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = { normalizeApiResponse, runTests, simulateActualUsage };
} else {
  // 浏览器环境
  runTests();
  simulateActualUsage();
}
