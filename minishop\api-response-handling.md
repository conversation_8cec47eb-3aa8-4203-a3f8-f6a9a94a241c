# API响应处理最佳实践

## 问题描述

在小程序开发中，经常遇到以下问题：
1. 后端API返回格式不统一
2. 前端期望数组但收到对象，导致 `map is not a function` 错误
3. 前端期望对象但收到数组，导致属性访问错误
4. 缺乏类型检查，运行时才发现数据结构问题

## 解决方案

### 1. 统一后端API返回格式（推荐）

**标准化响应格式：**
```javascript
// 成功响应
{
  "success": true,
  "data": [...] | {...},
  "message": "操作成功"
}

// 分页响应
{
  "success": true,
  "data": {
    "records": [...],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}

// 错误响应
{
  "success": false,
  "message": "错误信息",
  "code": "ERROR_CODE"
}
```

### 2. 前端添加全局响应拦截器

**使用统一的响应处理函数：**
```javascript
// 在 utils/api.js 中
const apiService = require('./api.js');

// 获取资产列表
apiService.getAssetsList(params)
  .then((response) => {
    // 使用统一的响应处理
    const assets = apiService.normalizeApiResponse(response, 'array');
    
    // 现在可以安全地使用数组方法
    const processedAssets = assets.map(asset => ({
      ...asset,
      // 添加计算字段
    }));
  })
  .catch((error) => {
    console.error('获取资产失败:', error);
  });
```

**使用响应拦截器：**
```javascript
const { arrayInterceptor, objectInterceptor } = require('../utils/response-interceptor');

// 处理数组响应
apiService.getAssetsList(params)
  .then(response => arrayInterceptor(response))
  .then(assets => {
    // assets 保证是数组
    const processedAssets = assets.map(asset => ({...asset}));
  });

// 处理对象响应
apiService.getAssetDetail(assetId)
  .then(response => objectInterceptor(response))
  .then(asset => {
    // asset 保证是对象
    console.log(asset.name);
  });
```

### 3. 使用TypeScript定义接口类型

**定义API响应类型：**
```typescript
// types/api.d.ts
export interface Asset {
  id: number;
  name: string;
  product_expiry_date: string;
  // ... 其他字段
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 使用类型
const getAssetsList = (): Promise<ApiResponse<Asset[]>> => {
  return apiService.get('/assets');
};
```

## 实际修复示例

### 修复前的问题代码：
```javascript
// me.js - 问题代码
apiService.getAssetsList(params)
  .then((assets) => {
    // 错误：假设 assets 是数组，但实际可能是 {records: [], total: 1}
    const processedAssets = assets.map(asset => ({...asset})); // TypeError: assets.map is not a function
  });
```

### 修复后的代码：
```javascript
// me.js - 修复后
apiService.getAssetsList(params)
  .then((response) => {
    // 使用统一的响应处理函数
    const assets = apiService.normalizeApiResponse(response, 'array');
    
    // 现在可以安全地使用数组方法
    const processedAssets = assets.map(asset => ({
      ...asset,
      productExpiryStatus: this.getExpiryStatus(asset.product_expiry_date),
      spsExpiryStatus: this.getExpiryStatus(asset.sps_expiry_date)
    }));
  });
```

## 最佳实践建议

### 1. 后端开发建议
- 统一所有API的响应格式
- 使用标准的HTTP状态码
- 提供清晰的错误信息
- 文档化API响应格式

### 2. 前端开发建议
- 始终使用响应处理函数
- 添加类型检查和默认值
- 记录详细的错误日志
- 编写单元测试验证数据处理

### 3. 团队协作建议
- 制定API设计规范
- 使用TypeScript增强类型安全
- 建立代码审查流程
- 定期重构和优化

## 工具函数使用指南

### normalizeApiResponse 函数
```javascript
// 处理数组响应
const assets = apiService.normalizeApiResponse(response, 'array');
// 支持格式: [], {records: []}, {data: []}, {success: true, data: []}

// 处理对象响应
const asset = apiService.normalizeApiResponse(response, 'object');
// 支持格式: {}, {data: {}}, {success: true, data: {}}
```

### 响应拦截器
```javascript
const { ResponseInterceptor } = require('../utils/response-interceptor');

// 自定义拦截器
const customInterceptor = ResponseInterceptor.create({
  expectArray: true,
  logErrors: true,
  defaultValue: []
});

// 使用拦截器
const processedData = customInterceptor(apiResponse);
```

## 错误处理

### 常见错误类型
1. `TypeError: xxx.map is not a function` - 期望数组但收到其他类型
2. `TypeError: Cannot read property 'xxx' of undefined` - 期望对象但收到undefined
3. `TypeError: xxx.filter is not a function` - 数组方法调用失败

### 防御性编程
```javascript
// 添加类型检查
const processAssets = (response) => {
  const assets = apiService.normalizeApiResponse(response, 'array');
  
  // 双重保险
  if (!Array.isArray(assets)) {
    console.warn('资产数据不是数组格式:', assets);
    return [];
  }
  
  return assets.map(asset => ({...asset}));
};
```

通过以上方法，可以有效避免API响应格式不一致导致的运行时错误，提高代码的健壮性和可维护性。
