// pages/me.js
const navService = require('../../utils/navigator.js');
const apiService = require('../../utils/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      avatarUrl: '',
      nickName: '点击登录',
      displayUserId: '',
      companyName: '',
      userId: '',
      email: '',
      isLoggedIn: false
    },
    // 企业相关
    currentEnterprise: {},
    enterprises: [],
    // 资产相关
    assets: [], // 用户资产列表
    activeAssets: [], // 有效资产
    expiredAssets: [], // 过期资产
    filteredAssets: [], // 筛选后的资产
    assetFilter: 'all', // 资产筛选：all, active, expired
    // 订单相关
    orders: [], // 所有订单
    pendingOrders: [], // 待付款订单
    paidOrders: [], // 已付款订单
    filteredOrders: [], // 筛选后的订单
    orderFilter: 'all', // 订单筛选：all, pending, paid
    // 页签相关
    activeTab: 'assets', // 当前选中的选项卡: assets, orders
    // 弹窗相关
    showEnterpriseModal: false,
    showRenewModal: false,
    showUserEditModal: false,
    currentAssetId: '',
    selectedDuration: 12,
    selectedUsers: 1,
    availableModules: [],
    totalPrice: 0,
    // 用户编辑表单
    editForm: {
      nickname: '',
      email: ''
    },
    // 加载状态
    loading: {
      assets: false,
      orders: false,
      userInfo: false
    },
    // 用于动画效果
    headerAnimation: {},
    cardAnimation: [],

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查登录状态
    this.checkLoginStatus();

    // 获取状态栏高度设置CSS变量
    const windowInfo = wx.getWindowInfo();
    const statusBarHeight = windowInfo.statusBarHeight;
    // 设置CSS变量
    wx.nextTick(() => {
      this.setData({
        '--status-bar-height': statusBarHeight + 'px'
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查登录状态并刷新数据
    this.checkLoginStatus();
    // 使用 onShow 来确保每次进入页面都能触发动画
    this.playAnimations();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新时重新获取数据
    this.refreshAllData()
      .finally(() => {
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果有分页，这里可以加载更多数据
    const currentTab = this.data.activeTab;
    
    if (currentTab === 'assets' && this.data.assets.length >= 20) {
      // 加载更多资产
    } else if (currentTab === 'pending' && this.data.pendingOrders.length >= 20) {
      // 加载更多待付款订单
    } else if (currentTab === 'paid' && this.data.paidOrders.length >= 20) {
      // 加载更多已付款订单
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的个人中心',
      path: '/pages/me/me'
    }
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '贝克智软 - 一站式企业管理解决方案',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    }
  },

  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const { tab } = e.detail;
    navService.handleBottomNav(tab);
  },

  /**
   * 导航到首页
   */
  navigateToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 切换主页签（资产/订单）
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });

    // 如果切换到订单页签，确保订单数据已加载
    if (tab === 'orders' && this.data.orders.length === 0) {
      this.getOrdersList();
    }
  },

  /**
   * 切换订单筛选
   */
  switchOrderFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      orderFilter: filter
    });
    this.filterOrders();
  },

  /**
   * 筛选订单
   */
  filterOrders() {
    const { orders, orderFilter } = this.data;
    let filteredOrders = [];

    switch (orderFilter) {
      case 'pending':
        filteredOrders = orders.filter(order =>
          order.payment_status === '待付款' || order.payment_status === 'pending'
        );
        break;
      case 'paid':
        filteredOrders = orders.filter(order =>
          order.payment_status === '已付款' || order.payment_status === 'paid'
        );
        break;
      default:
        filteredOrders = orders;
        break;
    }

    this.setData({
      filteredOrders
    });
  },

  /**
   * 切换资产筛选
   */
  switchAssetFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      assetFilter: filter
    });
    this.updateFilteredAssets();
  },

  /**
   * 更新筛选后的资产列表
   */
  updateFilteredAssets() {
    const { assets, activeAssets, expiredAssets, assetFilter } = this.data;
    let filteredAssets = [];

    switch (assetFilter) {
      case 'active':
        filteredAssets = activeAssets;
        break;
      case 'expired':
        filteredAssets = expiredAssets;
        break;
      default:
        filteredAssets = assets;
        break;
    }

    this.setData({
      filteredAssets
    });
  },

  /**
   * 显示企业选择器
   */
  showEnterpriseSelector() {
    if (this.data.enterprises.length <= 1) {
      wx.showToast({
        title: '当前只有一个企业',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showEnterpriseModal: true
    });
  },

  /**
   * 隐藏企业选择弹窗
   */
  hideEnterpriseModal() {
    this.setData({
      showEnterpriseModal: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  /**
   * 选择企业
   */
  selectEnterprise(e) {
    const enterprise = e.currentTarget.dataset.enterprise;
    this.switchEnterprise(enterprise);
    this.hideEnterpriseModal();
  },

  /**
   * 切换企业
   */
  switchEnterprise(enterprise) {
    this.setData({
      currentEnterprise: enterprise,
      assets: [],
      activeAssets: [],
      expiredAssets: [],
      filteredAssets: [],
      orders: [],
      filteredOrders: []
    });

    // 重新加载当前企业的数据
    this.loadEnterpriseData();
  },

  /**
   * 加载企业数据
   */
  loadEnterpriseData() {
    if (!this.data.currentEnterprise.id) {
      return;
    }

    // 总是加载资产数据和订单数据，确保数据完整性
    this.getAssetsList();
    this.getOrdersList();
  },

  /**
   * 查看通知
   */
  viewNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications',
    });
  },

  /**
   * 编辑用户信息
   */
  editUserInfo() {
    if (!this.data.userInfo.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showUserEditModal: true,
      editForm: {
        nickname: this.data.userInfo.nickName || '',
        email: this.data.userInfo.email || ''
      }
    });
  },

  /**
   * 隐藏用户编辑弹窗
   */
  hideUserEditModal() {
    this.setData({
      showUserEditModal: false,
      editForm: {
        nickname: '',
        email: ''
      }
    });
  },

  /**
   * 处理表单输入
   */
  handleFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`editForm.${field}`]: value
    });
  },

  /**
   * 保存用户信息
   */
  saveUserInfo() {
    const { nickname, email } = this.data.editForm;

    if (!nickname.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    // 邮箱格式验证（如果填写了邮箱）
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    // 调用API更新用户信息
    apiService.put(`/users/profile/${this.data.userInfo.userId}`, {
      nickname: nickname.trim(),
      email: email.trim()
    })
    .then(() => {
      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 更新本地显示
      this.setData({
        'userInfo.nickName': nickname.trim(),
        'userInfo.email': email.trim()
      });

      // 隐藏弹窗
      this.hideUserEditModal();

      // 重新获取用户信息以确保数据同步
      this.getUserInfo();
    })
    .catch((error) => {
      wx.hideLoading();
      console.error('保存用户信息失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  },

  /**
   * 联系客服
   */
  contactService(e) {
    const assetId = e.currentTarget.dataset.id;
    // 查找当前资产
    const asset = this.data.assets.find(item => item.id === assetId);
    
    if (!asset) return;
    
    wx.showActionSheet({
      itemList: ['在线客服', '电话咨询', '提交工单'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 在线客服
            wx.navigateTo({
              url: '/pages/customer-service/online?assetId=' + assetId,
            });
            break;
          case 1: // 电话咨询
            wx.makePhoneCall({
              phoneNumber: '************',
              fail: () => {
                wx.showToast({
                  title: '拨打失败，请手动拨打',
                  icon: 'none'
                });
              }
            });
            break;
          case 2: // 提交工单
            wx.navigateTo({
              url: '/pages/customer-service/ticket?assetId=' + assetId + '&productName=' + asset.name,
            });
            break;
        }
      }
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const isLoggedIn = apiService.isLoggedIn();
    const localUserInfo = apiService.getLocalUserInfo();

    if (isLoggedIn && localUserInfo) {
      // 已登录，获取用户信息并加载数据
      this.setData({
        'userInfo.isLoggedIn': true,
        'userInfo.nickName': localUserInfo.nickname || localUserInfo.name || '用户',
        'userInfo.displayUserId': localUserInfo.user_id || localUserInfo.id || '',
        'userInfo.companyName': localUserInfo.enterprise?.name || '',
        'userInfo.userId': localUserInfo.id || '',
        'userInfo.email': localUserInfo.email || ''
      });

      // 加载用户数据
      this.loadUserData();
    } else {
      // 未登录，显示登录提示，清理所有数据
      this.setData({
        'userInfo.isLoggedIn': false,
        'userInfo.nickName': '点击登录',
        'userInfo.companyName': '',
        'userInfo.userId': '',
        'userInfo.email': '',
        'userInfo.displayUserId': '',
        // 清理企业相关数据
        enterprises: [],
        currentEnterprise: {},
        // 清理资产数据
        assets: [],
        activeAssets: [],
        expiredAssets: [],
        filteredAssets: [],
        // 清理订单数据
        pendingOrders: [],
        paidOrders: [],
        orders: [],
        filteredOrders: []
      });
    }
  },

  /**
   * 加载用户数据
   */
  loadUserData() {
    // 首先加载用户关联的企业列表，然后根据当前企业加载数据
    this.getEnterprisesList().then(() => {
      // 企业列表加载完成后，加载当前企业的数据
      this.loadEnterpriseData();
    });
  },

  /**
   * 加载用户资产数据
   */
  loadUserAssets() {
    if (!apiService.isLoggedIn()) {
      return Promise.resolve();
    }

    // 使用资产API，后端会根据用户类型自动过滤
    return apiService.get('/assets').then(response => {
      console.log('用户资产响应:', response);

      if (response && Array.isArray(response)) {
        // 处理资产数据
        const now = new Date();
        const activeAssets = [];
        const expiredAssets = [];

        response.forEach(asset => {
          const expiryDate = new Date(asset.expiry_date);
          if (expiryDate > now) {
            activeAssets.push(asset);
          } else {
            expiredAssets.push(asset);
          }
        });

        this.setData({
          assets: response,
          activeAssets: activeAssets,
          expiredAssets: expiredAssets,
          filteredAssets: response
        });
      }
    }).catch(error => {
      console.error('获取用户资产失败:', error);
      // 如果是权限错误，不显示错误提示，只是不加载数据
      if (error.message && !error.message.includes('权限')) {
        wx.showToast({
          title: '加载资产失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 加载用户订单数据
   */
  loadUserOrders() {
    if (!apiService.isLoggedIn()) {
      return Promise.resolve();
    }

    // 使用订单API，后端会根据用户类型自动过滤
    return apiService.get('/orders').then(response => {
      console.log('用户订单响应:', response);

      if (response && Array.isArray(response)) {
        // 处理订单数据
        const pendingOrders = response.filter(order => order.payment_status === '待支付');
        const paidOrders = response.filter(order => order.payment_status === '已支付');

        this.setData({
          pendingOrders: pendingOrders,
          paidOrders: paidOrders
        });
      }
    }).catch(error => {
      console.error('获取用户订单失败:', error);
      // 如果是权限错误，不显示错误提示，只是不加载数据
      if (error.message && !error.message.includes('权限')) {
        wx.showToast({
          title: '加载订单失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取企业列表（用户关联的企业）
   */
  getEnterprisesList() {
    if (!apiService.isLoggedIn()) {
      return Promise.resolve();
    }

    return apiService.get('/enterprises').then(response => {
      console.log('企业列表响应:', response);

      if (response && Array.isArray(response)) {
        this.setData({
          enterprises: response
        });

        // 如果没有当前企业，设置第一个为当前企业
        if (!this.data.currentEnterprise.id && response.length > 0) {
          this.setData({
            currentEnterprise: response[0]
          });
        }
      }
    }).catch(error => {
      console.error('获取企业列表失败:', error);
    });
  },

  /**
   * 刷新所有数据
   */
  refreshAllData() {
    if (!apiService.isLoggedIn()) {
      return Promise.resolve();
    }

    return Promise.allSettled([
      this.getAssetsList(),
      this.getOrdersList()
    ]);
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (!apiService.isLoggedIn()) {
      return Promise.resolve();
    }

    this.setData({ 'loading.userInfo': true });

    return apiService.getCurrentUser()
      .then((userInfo) => {
        console.log('获取用户信息成功:', userInfo);
        this.setData({
          'userInfo.nickName': userInfo.nickname || userInfo.name || '用户',
          'userInfo.displayUserId': userInfo.user_id || '',
          'userInfo.companyName': userInfo.enterprise?.name || '',
          'userInfo.userId': userInfo.id || '',
          'userInfo.email': userInfo.email || '',
          'userInfo.isLoggedIn': true
        });
      })
      .catch((error) => {
        console.error('获取用户信息失败:', error);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ 'loading.userInfo': false });
      });
  },

  /**
   * 获取资产列表
   */
  getAssetsList() {
    if (!apiService.isLoggedIn() || !this.data.currentEnterprise.id) {
      return Promise.resolve();
    }

    this.setData({ 'loading.assets': true });

    const params = {
      enterpriseId: this.data.currentEnterprise.id
    };

    return apiService.getAssetsList(params)
      .then((response) => {
        console.log('获取资产列表成功:', response);

        // 使用统一的API响应处理函数
        const assets = apiService.normalizeApiResponse(response, 'array');

        // 处理资产数据，添加计算字段
        const processedAssets = assets.map(asset => {
          return {
            ...asset,
            // 计算到期状态
            productExpiryStatus: this.getExpiryStatus(asset.product_expiry_date),
            spsExpiryStatus: this.getExpiryStatus(asset.sps_expiry_date)
          };
        });

        // 分类资产
        const activeAssets = processedAssets.filter(asset =>
          asset.productExpiryStatus !== 'expired' && asset.spsExpiryStatus !== 'expired'
        );
        const expiredAssets = processedAssets.filter(asset =>
          asset.productExpiryStatus === 'expired' || asset.spsExpiryStatus === 'expired'
        );

        this.setData({
          assets: processedAssets,
          activeAssets: activeAssets,
          expiredAssets: expiredAssets
        });

        // 更新筛选后的资产列表
        this.updateFilteredAssets();
      })
      .catch((error) => {
        console.error('获取资产列表失败:', error);
        wx.showToast({
          title: '获取资产列表失败',
          icon: 'none'
        });
        // 设置空数组，避免显示错误数据
        this.setData({
          assets: [],
          activeAssets: [],
          expiredAssets: [],
          filteredAssets: []
        });
      })
      .finally(() => {
        this.setData({ 'loading.assets': false });
      });
  },

  /**
   * 获取订单列表（包括待付款和已付款）
   */
  getOrdersList() {
    if (!apiService.isLoggedIn() || !this.data.currentEnterprise.id) {
      return Promise.resolve();
    }

    this.setData({ 'loading.orders': true });

    const params = {
      enterprise_id: this.data.currentEnterprise.id
    };

    return apiService.getOrdersList(params)
      .then((response) => {
        console.log('获取订单列表成功:', response);

        // 使用统一的API响应处理函数
        const orders = apiService.normalizeApiResponse(response, 'array');
        console.log('处理后的订单数据:', orders);
        console.log('订单数组长度:', orders.length);

        // 根据订单状态分类
        const pendingOrders = orders.filter(order =>
          order.payment_status === '待付款' || order.payment_status === 'pending'
        );
        const paidOrders = orders.filter(order =>
          order.payment_status === '已付款' || order.payment_status === 'paid'
        );

        console.log('待付款订单:', pendingOrders.length);
        console.log('已付款订单:', paidOrders.length);

        this.setData({
          orders: orders,
          pendingOrders: pendingOrders,
          paidOrders: paidOrders
        });

        // 更新筛选后的订单
        this.filterOrders();
      })
      .catch((error) => {
        console.error('获取订单列表失败:', error);
        wx.showToast({
          title: '获取订单列表失败',
          icon: 'none'
        });
        // 设置空数组，避免显示错误数据
        this.setData({
          orders: [],
          pendingOrders: [],
          paidOrders: [],
          filteredOrders: []
        });
      })
      .finally(() => {
        this.setData({ 'loading.orders': false });
      });
  },

  /**
   * 获取待付款订单（保持向后兼容）
   */
  getPendingOrders() {
    return this.getOrdersList();
  },

  /**
   * 获取已付款订单（保持向后兼容）
   */
  getPaidOrders() {
    return this.getOrdersList();
  },

  /**
   * 计算剩余天数
   */
  calculateDaysLeft(expiryDateStr) {
    if (!expiryDateStr) return 0;
    const today = new Date();
    const expiryDate = new Date(expiryDateStr);
    const timeDiff = expiryDate.getTime() - today.getTime();
    return Math.max(0, Math.ceil(timeDiff / (1000 * 3600 * 24)));
  },

  /**
   * 获取到期状态
   * @param {string} expiryDateStr - 到期日期字符串
   * @returns {Object} 包含状态类名和文本的对象
   */
  getExpiryStatus(expiryDateStr) {
    if (!expiryDateStr) {
      return { class: 'normal', text: '无限期' };
    }

    const daysLeft = this.calculateDaysLeft(expiryDateStr);

    if (daysLeft <= 0) {
      return { class: 'expired', text: '已过期' };
    } else if (daysLeft <= 30) {
      return { class: 'warning', text: `${daysLeft}天后到期` };
    } else {
      return { class: 'normal', text: `${daysLeft}天后到期` };
    }
  },

  /**
   * 处理用户头像点击 - 登录/注销
   */
  handleUserInfoTap() {
    if (this.data.userInfo.isLoggedIn) {
      // 已登录，直接显示退出登录确认
      this.logout();
    } else {
      // 未登录，执行登录
      this.login();
    }
  },

  /**
   * 用户登录
   */
  login() {
    // 跳转到新的登录页面
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },









  /**
   * 用户注销
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息
          apiService.clearLoginInfo();

          // 重置页面状态
          this.checkLoginStatus();

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },



  /**
   * 复制用户ID
   */
  copyUserId() {
    const userId = this.data.userInfo.displayUserId;
    if (!userId) {
      wx.showToast({
        title: '用户ID未设置',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: userId,
      success: () => {
        wx.showToast({
          title: '用户ID已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看资产详情
   */
  viewAssetDetail(e) {
    const assetId = e.currentTarget.dataset.id;
    if (!assetId) return;

    // 这里可以跳转到资产详情页面
    wx.showToast({
      title: '资产详情页面开发中',
      icon: 'none'
    });
  },

  /**
   * 查看全部订单 - 显示所有订单
   */
  viewAllOrders() {
    // 切换到订单标签页并显示所有订单
    this.setData({
      activeTab: 'orders',
      orderFilter: 'all'
    });

    // 滚动到订单区域
    wx.pageScrollTo({
      selector: '.order-section',
      duration: 300
    });
  },

  /**
   * 导航到设置页面
   */
  navigateToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings',
    });
  },

  /**
   * 查看全部资产 - 显示所有资产
   */
  viewAllAssets() {
    // 切换到资产标签页并显示所有资产
    this.setData({
      activeTab: 'assets',
      assetFilter: 'all'
    });

    // 滚动到资产区域
    wx.pageScrollTo({
      selector: '.assets-section',
      duration: 300
    });
  },

  /**
   * 导航到产品列表
   */
  navigateToProducts() {
    wx.switchTab({
      url: '/pages/index/index',
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/order-detail/order-detail?id=' + orderId,
    });
  },

  /**
   * 取消订单
   */
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认取消',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 模拟取消订单的API请求
          wx.showLoading({
            title: '取消中',
          });
          
          setTimeout(() => {
            // 移除已取消的订单
            const pendingOrders = this.data.pendingOrders.filter(order => order.id !== orderId);
            this.setData({
              pendingOrders
            });
            
            wx.hideLoading();
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            });
          }, 1000);
        }
      }
    });
  },

  /**
   * 支付订单
   */
  payOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.pendingOrders.find(item => item.id === orderId);
    
    if (!order) return;
    
    wx.showModal({
      title: '确认支付',
      content: `需支付: ¥${order.price}`,
      confirmText: '立即支付',
      success: (res) => {
        if (res.confirm) {
          // 模拟支付流程
          wx.showLoading({
            title: '处理中',
          });
          
          setTimeout(() => {
            wx.hideLoading();
            
            // 模拟支付成功
            wx.showToast({
              title: '支付成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                // 从待付款移到已付款
                const pendingOrders = this.data.pendingOrders.filter(o => o.id !== orderId);
                const paidOrder = {...order};
                paidOrder.transactionId = 'TX' + new Date().getTime().toString().substr(3);
                
                const paidOrders = [paidOrder, ...this.data.paidOrders];
                
                this.setData({
                  pendingOrders,
                  paidOrders
                });
              }
            });
          }, 1500);
        }
      }
    });
  },

  /**
   * 显示续费选项
   */
  showRenewOptions(e) {
    const assetId = e.currentTarget.dataset.id;
    // 查找对应的资产信息
    const asset = this.data.assets.find(item => item.id === assetId);
    
    if (!asset) return;
    
    // 根据资产信息准备可用模块
    const availableModules = [
      { id: 'mod001', name: '财务核算', selected: asset.modules.includes('财务核算') },
      { id: 'mod002', name: '资金管理', selected: asset.modules.includes('资金管理') },
      { id: 'mod003', name: '报表分析', selected: asset.modules.includes('报表分析') },
      { id: 'mod004', name: '采购管理', selected: asset.modules.includes('采购管理') },
      { id: 'mod005', name: '销售管理', selected: asset.modules.includes('销售管理') },
      { id: 'mod006', name: '库存管理', selected: asset.modules.includes('库存管理') },
      { id: 'mod007', name: '供应链', selected: asset.modules.includes('供应链') },
      { id: 'mod008', name: '人力资源', selected: asset.modules.includes('人力') },
      { id: 'mod009', name: '项目管理', selected: asset.modules.includes('项目') },
      { id: 'mod010', name: 'CRM', selected: asset.modules.includes('CRM') },
      { id: 'mod011', name: '生产管理', selected: asset.modules.includes('生产') }
    ];
    
    this.setData({
      showRenewModal: true,
      currentAssetId: assetId,
      availableModules: availableModules,
      totalPrice: this.calculateTotalPrice(12, 1, availableModules.filter(m => m.selected).length)
    });
  },

  /**
   * 隐藏续费弹窗
   */
  hideRenewModal() {
    this.setData({
      showRenewModal: false
    });
  },

  /**
   * 选择服务时长
   */
  selectDuration(e) {
    const duration = parseInt(e.currentTarget.dataset.value);
    this.setData({
      selectedDuration: duration,
      totalPrice: this.calculateTotalPrice(
        duration, 
        this.data.selectedUsers, 
        this.data.availableModules.filter(m => m.selected).length
      )
    });
  },

  /**
   * 减少用户数量
   */
  decreaseUsers() {
    if (this.data.selectedUsers <= 1) return;
    
    const newCount = this.data.selectedUsers - 1;
    this.setData({
      selectedUsers: newCount,
      totalPrice: this.calculateTotalPrice(
        this.data.selectedDuration, 
        newCount, 
        this.data.availableModules.filter(m => m.selected).length
      )
    });
  },

  /**
   * 增加用户数量
   */
  increaseUsers() {
    const newCount = this.data.selectedUsers + 1;
    this.setData({
      selectedUsers: newCount,
      totalPrice: this.calculateTotalPrice(
        this.data.selectedDuration, 
        newCount, 
        this.data.availableModules.filter(m => m.selected).length
      )
    });
  },

  /**
   * 用户输入数量
   */
  inputUsers(e) {
    const newCount = parseInt(e.detail.value) || 1;
    this.setData({
      selectedUsers: newCount,
      totalPrice: this.calculateTotalPrice(
        this.data.selectedDuration, 
        newCount, 
        this.data.availableModules.filter(m => m.selected).length
      )
    });
  },

  /**
   * 切换模块选择状态
   */
  toggleModule(e) {
    const moduleId = e.currentTarget.dataset.id;
    const modules = this.data.availableModules.map(m => {
      if (m.id === moduleId) {
        return { ...m, selected: !m.selected };
      }
      return m;
    });
    
    this.setData({
      availableModules: modules,
      totalPrice: this.calculateTotalPrice(
        this.data.selectedDuration, 
        this.data.selectedUsers, 
        modules.filter(m => m.selected).length
      )
    });
  },

  /**
   * 计算总价
   */
  calculateTotalPrice(duration, users, moduleCount) {
    // 简单的价格计算逻辑
    const basePrice = 1000; // 基础价格
    const durationFactor = duration / 12; // 时长因子
    const userPrice = 200 * users; // 每用户价格
    const modulePrice = 300 * moduleCount; // 每模块价格
    
    return Math.round((basePrice + userPrice + modulePrice) * durationFactor);
  },

  /**
   * 确认续费
   */
  confirmRenew() {
    // 获取当前选中的模块
    const selectedModules = this.data.availableModules
      .filter(m => m.selected)
      .map(m => m.name);
      
    // 构建续费请求数据
    const renewData = {
      assetId: this.data.currentAssetId,
      duration: this.data.selectedDuration,
      users: this.data.selectedUsers,
      modules: selectedModules,
      totalPrice: this.data.totalPrice
    };
    
    // 这里应该调用API提交续费请求
    // 模拟API调用
    wx.showLoading({
      title: '处理中',
    });
    
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟支付流程
      wx.showModal({
        title: '确认支付',
        content: `续费总额: ¥${this.data.totalPrice}`,
        confirmText: '立即支付',
        success: (res) => {
          if (res.confirm) {
            // 模拟支付成功
            wx.showToast({
              title: '支付成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                // 更新本地资产信息
                this.hideRenewModal();
                this.getAssetsList();
              }
            });
          }
        }
      });
    }, 1000);
  },

  /**
   * 播放入场动画
   */
  playAnimations: function() {
    // --- 重置动画 ---
    this.setData({
      headerAnimation: wx.createAnimation({ duration: 0 }).translateY(-30).opacity(0).step().export(),
      cardAnimation: [
        wx.createAnimation({ duration: 0 }).translateY(30).opacity(0).step().export()
      ]
    });

    // --- 执行入场动画 ---
    setTimeout(() => {
      // 头部动画
      const headerAnimation = wx.createAnimation({
        duration: 600,
        timingFunction: 'ease-out',
      });
      headerAnimation.translateY(0).opacity(1).step();
      
      // 订单卡片的动画
      const cardAnimation = wx.createAnimation({ duration: 600, timingFunction: 'cubic-bezier(0.2, 0.8, 0.2, 1)' });
      cardAnimation.translateY(0).opacity(1).step();

      this.setData({
        headerAnimation: headerAnimation.export(),
        'cardAnimation[0]': cardAnimation.export(),
      });
    }, 50);
  },

  /**
   * 查看资产详情
   */
  viewAssetDetail(e) {
    const assetId = e.currentTarget.dataset.id;
    if (!assetId) {
      wx.showToast({
        title: '资产ID不存在',
        icon: 'none'
      });
      return;
    }

    console.log('点击查看资产详情，ID:', assetId);

    wx.navigateTo({
      url: `/pages/asset-detail/asset-detail?id=${assetId}`,
      fail: (error) => {
        console.error('跳转资产详情页失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },


})